# CheeStack Flutter 前端

> 基于 Flutter 的跨平台智能学习应用

## 📖 项目概述

### 功能简介
CheeStack Flutter 前端是一个跨平台移动学习应用，支持iOS和Android系统。集成了语音识别、间隔重复学习、多媒体卡片等功能，为用户提供智能化的学习体验。

### 核心特性
- ✅ **语音识别** - 集成Sherpa-ONNX实现本地实时语音转文字
- ✅ **智能学习** - 基于间隔重复算法的学习系统 (Study + Learn双系统)
- ✅ **多媒体支持** - 图片、音频、视频学习内容
- ✅ **汉字练习** - 支持汉字笔顺练习和书写
- ✅ **数据同步** - 多设备间无缝数据同步，支持冲突解决和错误恢复
- ✅ **离线支持** - 完整的本地数据库，支持离线学习
- ✅ **跨平台** - 统一的iOS/Android用户体验
- ✅ **卡片编辑器** - 所见即所得的卡片编辑体验，支持实时预览

### 技术实现
- **框架**: Flutter 3.x + Dart 3.x
- **状态管理**: GetX (响应式状态管理)
- **语音识别**: Sherpa-ONNX (本地语音识别引擎)
- **音频处理**: Just Audio + Flutter Sound
- **网络请求**: Dio + HTTP拦截器
- **本地存储**: SharedPreferences + SQLite

## 🏗️ 架构设计

### 目录结构
```
lib/
├── main.dart                   # 应用入口
├── global.dart                 # 全局配置
├── theme.dart                  # 主题配置
├── routes/                     # 路由管理
├── pages/                      # 页面组件
│   ├── application/            # 主应用框架
│   ├── home/                   # 首页
│   ├── study/                  # 学习页面
│   ├── listening/              # 听力练习
│   ├── creation/               # 内容创建
│   ├── profile/                # 个人中心
│   └── auth/                   # 认证页面
├── controllers/                # 状态管理控制器
├── models/                     # 数据模型
├── apis/                       # API接口封装
├── services/                   # 业务服务
├── common/                     # 通用组件和工具
└── i18n/                       # 国际化
```

### 业务流程
1. **用户认证** → JWT token获取和存储
2. **数据同步** → 本地数据与服务器同步
3. **学习调度** → 基于算法的学习内容推荐
4. **语音交互** → 实时语音识别和TTS反馈
5. **进度跟踪** → 学习记录和统计分析

## 🚀 快速开始

### 环境要求
- Flutter >= 3.0
- Dart >= 3.0
- Android Studio / Xcode (用于设备调试)
- 后端服务 (CheeStack FastAPI)

### 安装运行
```bash
# 1. 进入前端目录
cd cheestack-flt

# 2. 安装依赖
flutter pub get

# 3. 运行项目 (确保后端服务已启动)
flutter run

# 4. 构建发布版本
flutter build apk --release  # Android
flutter build ios --release  # iOS
```

### 基本使用
1. **启动应用** - 确保后端服务运行在 http://localhost:8000
2. **用户注册** - 通过手机号验证码注册账号
3. **创建书籍** - 组织学习内容到不同书籍分类
4. **添加卡片** - 创建包含多媒体内容的学习卡片
5. **开始学习** - 系统智能推荐学习和复习内容
6. **语音练习** - 使用语音识别功能进行口语练习

## 🔌 API接口

### 主要接口
- `POST /api/v1/auth/login` - 用户登录
- `GET /v1/books` - 获取书籍列表
- `POST /api/v1/cards` - 创建学习卡片
- `GET /v1/books/{id}/study` - 获取学习内容
- `POST /api/v1/study/record` - 提交学习记录

### 使用示例

#### Study系统 (原版学习系统)
```dart
// 用户登录
final response = await UserApi.login(
  mobile: '13800138000',
  password: 'password'
);

// 获取书籍列表
final books = await BookApi.getBooks(page: 1, size: 20);

// 提交学习记录
await StudyApi.submitRecord(
  cardId: 'card-id',
  isCorrect: true,
  duration: 5000
);
```

#### Learn系统 (新学习系统)
```dart
// 初始化Learn控制器
final learnController = Get.find<LearnController>();

// 创建学习集合
await learnController.createCollection(
  name: '英语单词',
  description: '常用英语单词学习',
  type: 'language',
);

// 创建学习主题
await learnController.createTopic(
  collectionId: 'collection-sync-id',
  title: 'Apple',
  question: '苹果',
  answer: 'Apple',
  hint: '一种红色的水果',
);

// 开始学习会话
final studyController = Get.put(StudySessionController());
await studyController.startStudySession(maxTopics: 20);

// 数据同步
await learnController.syncData();
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/controllers/user_store_test.dart

# 运行集成测试
flutter test integration_test/
```

### 测试覆盖
- **单元测试**: 控制器逻辑、工具函数测试
- **Widget测试**: UI组件渲染和交互测试
- **集成测试**: 完整功能流程测试

## 🔧 依赖关系

### 核心依赖
- **flutter**: Flutter框架核心
- **get**: 状态管理和路由
- **dio**: HTTP网络请求
- **shared_preferences**: 本地数据存储
- **sherpa_onnx**: 语音识别引擎

### UI依赖
- **flutter_screenutil**: 屏幕适配
- **extended_image**: 图片处理
- **flutter_easyloading**: 加载提示
- **modal_bottom_sheet**: 底部弹窗

### 音频依赖
- **just_audio**: 音频播放
- **flutter_sound**: 音频录制
- **audio_session**: 音频会话管理
- **flutter_tts**: 文字转语音

### 外部依赖
- CheeStack FastAPI后端服务
- 腾讯云COS文件存储服务
- Sherpa-ONNX语音识别模型

## 📊 性能优化

### 内存管理
- 合理使用GetX控制器生命周期
- 及时释放音频和图片资源
- 避免内存泄漏和循环引用

### 渲染优化
- 使用ListView.builder进行列表优化
- 合理使用RepaintBoundary减少重绘
- 图片缓存和懒加载

### 网络优化
- HTTP请求缓存和重试机制
- 分页加载和预加载策略
- 离线数据支持

## 📚 文档

- [📖 架构文档](docs/ARCHITECTURE.md) - 项目整体架构和设计
- [🧩 组件文档](docs/COMPONENTS.md) - 主要组件说明和用法
  - [UI组件](docs/COMPONENTS/UI.md) - 界面组件库
  - [控制器组件](docs/COMPONENTS/CONTROLLERS.md) - 状态管理控制器
  - [服务组件](docs/COMPONENTS/SERVICES.md) - 业务服务层
  - [工具组件](docs/COMPONENTS/UTILS.md) - 工具函数库
- [⚡ 功能文档](docs/FEATURES/) - 各功能模块详细说明
  - [语音识别功能](docs/FEATURES/speech_recognition.md) - 语音相关功能
  - [汉字书写功能](docs/FEATURES/hanzi_writing.md) - 汉字练习功能
- [🚀 开发指南](docs/DEVELOPMENT_GUIDE.md) - 开发规范和最佳实践
- [📱 部署指南](docs/DEPLOYMENT.md) - 应用打包和发布
- [🔗 Learn模块集成](docs/LEARN_INTEGRATION.md) - 与Learn模块的完整集成指南
- [📋 Learn集成总结](docs/LEARN_INTEGRATION_SUMMARY.md) - Learn模块集成核心特性

## 📝 开发注意事项

### 重要规范
- 遵循Flutter官方开发规范
- 使用GetX进行状态管理，避免setState
- 合理使用async/await处理异步操作
- 保持代码结构清晰和模块化

### 常见问题
- **语音识别问题**: 确保Sherpa-ONNX模型文件正确配置
- **网络请求失败**: 检查后端服务是否正常运行
- **权限问题**: 确保应用具有必要的系统权限
- **构建失败**: 检查依赖版本兼容性

## 🔗 相关文档
- [项目总览](../README.md) - 整个项目的介绍
- [后端文档](../cheestack-fastapi/README.md) - FastAPI后端说明
- [API文档](../docs/api/README.md) - 接口参考文档
- [部署指南](../docs/deployment/README.md) - 完整部署说明

## 🤝 贡献指南

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 代码规范
- 遵循Dart官方代码风格
- 使用flutter_lints进行代码检查
- 编写必要的单元测试和Widget测试
- 更新相关文档

## 🔧 最近更新

### v2024.12.27 - 同步功能重大修复
- **修复同步错误**: 解决了`type 'Null' is not a subtype of type 'String'`错误
- **增强错误处理**: 添加了完善的null值检查和默认值处理
- **改进日志记录**: 增加了详细的同步过程日志，便于调试
- **优化用户体验**: 同步失败时不再中断整个流程，而是跳过问题数据继续处理
- **卡片编辑器**: 实现了所见即所得的卡片编辑功能，支持实时预览
- **同步状态监控**: 添加了简化的同步状态显示组件
- **前后端数据模型同步**: 修复了所有数据模型的fromJson方法，确保类型安全

### 核心修复内容
1. **数据模型类型安全**
   - BookModel.fromJson: 使用`.toString()`处理null值
   - CardModel.fromJson: 增强DateTime解析和默认值处理
   - UserModel.fromJson: 统一字符串类型转换
   - CardAsset.fromJson: 添加默认值和null安全处理

2. **同步服务增强**
   - 添加userId验证，防止空值传递
   - 为每个数据处理步骤添加try-catch包装
   - 单个数据失败不影响整体同步流程
   - 详细的调试日志输出

3. **错误恢复机制**
   - 跳过无效数据，继续处理后续数据
   - 提供详细的错误信息用于调试
   - 保持同步状态的一致性

### 测试覆盖
- ✅ **单元测试**: 数据模型的null值处理和默认值测试
- ✅ **Widget测试**: 同步状态组件的UI测试
- ✅ **集成测试**: 完整的同步流程测试
- ✅ **错误处理测试**: 网络错误和数据库错误的处理测试
- ✅ **前后端数据同步测试**: 验证API数据格式兼容性

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 开源协议。

---

**注意**: 本应用需要配合CheeStack FastAPI后端服务使用。请确保后端服务正常运行后再启动前端应用。
