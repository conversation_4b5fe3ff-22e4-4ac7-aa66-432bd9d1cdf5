import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:extended_image/extended_image.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import '../apis/profile_api.dart';

/// 用户信息编辑控制器
/// 专门负责编辑页面的状态管理，与主ProfileController完全隔离
class ProfileEditController extends GetxController {
  late final ProfileApiService _profileApi;

  // 表单控制器
  final usernameController = TextEditingController();
  final introController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // 状态变量
  final _isLoading = false.obs;
  final _hasChanges = false.obs;
  final _tempAvatarFile = Rx<File?>(null);

  // 原始数据（用于比较是否有变化）
  String _originalUsername = '';
  String _originalIntro = '';
  String _originalAvatarUrl = '';

  // Getters
  bool get isLoading => _isLoading.value;
  bool get hasChanges => _hasChanges.value;
  File? get tempAvatarFile => _tempAvatarFile.value;

  /// 当前显示的头像URL
  String get currentAvatarUrl {
    if (_tempAvatarFile.value != null) {
      return _tempAvatarFile.value!.path;
    }
    return _originalAvatarUrl;
  }

  /// 是否有临时头像
  bool get hasTempAvatar => _tempAvatarFile.value != null;

  @override
  void onInit() {
    super.onInit();
    _initializeApiService();
    _initializeData();
    _setupListeners();
  }

  /// 初始化API服务
  void _initializeApiService() {
    try {
      _profileApi = Get.find<ProfileApiService>();
    } catch (e) {
      // 如果ProfileApiService不存在，创建一个新的实例并注册
      _profileApi = ProfileApiService();
      Get.put(_profileApi);
    }
  }

  @override
  void onClose() {
    usernameController.dispose();
    introController.dispose();
    super.onClose();
  }

  /// 初始化数据
  void _initializeData() {
    final user = AuthController.to.usr.user;

    _originalUsername = user?.username ?? '';
    _originalIntro = user?.intro ?? '';
    _originalAvatarUrl = user?.avatar ?? '';

    usernameController.text = _originalUsername;
    introController.text = _originalIntro;
  }

  /// 设置监听器
  void _setupListeners() {
    usernameController.addListener(_checkForChanges);
    introController.addListener(_checkForChanges);
  }

  /// 检查是否有变化
  void _checkForChanges() {
    final hasUsernameChanged =
        usernameController.text.trim() != _originalUsername;
    final hasIntroChanged = introController.text.trim() != _originalIntro;
    final hasAvatarChanged = _tempAvatarFile.value != null;

    _hasChanges.value =
        hasUsernameChanged || hasIntroChanged || hasAvatarChanged;
  }

  /// 选择头像
  Future<void> selectAvatar() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 90,
      );

      if (image != null) {
        await _navigateToImageCrop(image.path);
      }
    } catch (e) {
      ShowToast.fail('选择头像失败: ${e.toString()}');
    }
  }

  /// 拍照选择头像
  Future<void> takePhoto() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 90,
      );

      if (image != null) {
        await _navigateToImageCrop(image.path);
      }
    } catch (e) {
      ShowToast.fail('拍摄头像失败: ${e.toString()}');
    }
  }

  /// 清除临时头像
  void clearTempAvatar() {
    _tempAvatarFile.value = null;
    _checkForChanges();
  }

  /// 预览头像
  void previewAvatar() {
    if (currentAvatarUrl.isNotEmpty) {
      CustomGallery.show(
        context: Get.context!,
        items: [
          CustomGalleryItem(
            type: hasTempAvatar ? SimageType.file : SimageType.network,
            url: currentAvatarUrl,
          ),
        ],
      );
    }
  }

  /// 保存用户信息
  Future<void> saveProfile() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (!hasChanges) {
      ShowToast.text('没有需要保存的修改');
      return;
    }

    _isLoading.value = true;

    try {
      String? newAvatarUrl;

      // 如果有新头像，先上传
      if (_tempAvatarFile.value != null) {
        newAvatarUrl = await _uploadAvatar(_tempAvatarFile.value!);
      }

      // 准备更新数据
      final updateData = <String, dynamic>{};

      final newUsername = usernameController.text.trim();
      final newIntro = introController.text.trim();

      if (newUsername != _originalUsername) {
        updateData['username'] = newUsername;
      }

      if (newIntro != _originalIntro) {
        updateData['intro'] = newIntro;
      }

      if (newAvatarUrl != null) {
        updateData['avatar'] = newAvatarUrl;
      }

      // 更新用户信息
      if (updateData.isNotEmpty) {
        final response =
            await _profileApi.updateUserProfile(profileData: updateData);

        if (response.isSuccess) {
          // 更新本地数据
          final currentUser = AuthController.to.usr.user;
          if (currentUser != null) {
            // 如果有新头像，删除旧头像
            if (updateData.containsKey('avatar')) {
              final oldAvatar = currentUser.avatar;
              if (oldAvatar != null &&
                  oldAvatar.isNotEmpty &&
                  oldAvatar != updateData['avatar']) {
                try {
                  Console.log('删除旧头像: $oldAvatar');
                  await OxCos().delete(oldAvatar);

                  // 清除图片缓存
                  try {
                    clearMemoryImageCache();
                    await clearDiskCachedImages();
                    await clearDiskCachedImage(oldAvatar);
                  } catch (cacheError) {
                    Console.log('清除缓存失败: $cacheError');
                  }
                  Console.log('旧头像已删除并清除缓存');
                } catch (e) {
                  Console.log('删除旧头像失败: $e');
                  // 不影响主流程，继续执行
                }
              }
            }

            // 更新用户信息
            if (updateData.containsKey('username')) {
              currentUser.username = updateData['username'];
            }
            if (updateData.containsKey('intro')) {
              currentUser.intro = updateData['intro'];
            }
            if (updateData.containsKey('avatar')) {
              currentUser.avatar = updateData['avatar'];
            }

            AuthController.to.updateUsr();
          }

          ShowToast.success('保存成功');
          Get.back(result: true); // 返回true表示有更新
        } else {
          throw Exception(response.message ?? '保存失败');
        }
      }
    } catch (e) {
      ShowToast.fail('保存失败: ${e.toString()}');
    } finally {
      _isLoading.value = false;
    }
  }

  /// 上传头像到COS
  Future<String> _uploadAvatar(File file) async {
    try {
      ShowToast.loading(val: '正在上传头像...');

      // 生成COS路径
      final cosPath = OxCos().getKey(
        path: file.path,
        type: UploadFileType.image,
        filename: 'avatar_${DateTime.now().millisecondsSinceEpoch}',
      );

      Console.log('开始上传头像到COS: $cosPath');

      // 上传到COS
      final uploadSuccess = await OxCos().upload(
        srcPath: file.path,
        cosPath: cosPath,
        onSuccess: (result, cosXmlResult) {
          Console.log('头像上传成功: $result');
        },
        onFail: (clientException, serviceException) {
          Console.log('头像上传失败: $clientException, $serviceException');
        },
      );

      ShowToast.dismiss();

      if (uploadSuccess) {
        // 构建完整的头像URL
        final avatarUrl = '${OxCos().baseUrl}$cosPath';
        Console.log('生成的头像URL: $avatarUrl');
        return avatarUrl;
      } else {
        throw Exception('上传到COS失败');
      }
    } catch (e) {
      ShowToast.dismiss();
      throw Exception('头像上传失败: ${e.toString()}');
    }
  }

  /// 取消编辑
  void cancelEdit() {
    if (hasChanges) {
      _showDiscardDialog();
    } else {
      Get.back();
    }
  }

  /// 显示放弃修改对话框
  void _showDiscardDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('放弃修改'),
        content: const Text('您有未保存的修改，确定要放弃吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('继续编辑'),
          ),
          TextButton(
            onPressed: () {
              Get.back(); // 关闭对话框
              Get.back(); // 返回上一页
            },
            child: const Text('放弃修改'),
          ),
        ],
      ),
    );
  }

  /// 用户名验证
  String? validateUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入用户名';
    }
    if (value.trim().length < 2) {
      return '用户名至少2个字符';
    }
    if (value.trim().length > 20) {
      return '用户名不能超过20个字符';
    }
    return null;
  }

  /// 简介验证
  String? validateIntro(String? value) {
    if (value != null && value.length > 200) {
      return '个人简介不能超过200个字符';
    }
    return null;
  }

  /// 导航到图片裁剪页面
  Future<void> _navigateToImageCrop(String imagePath) async {
    try {
      final result = await Get.toNamed(
        '/image_crop',
        arguments: {'imagePath': imagePath},
      );

      if (result != null && result is Map<String, dynamic>) {
        final croppedImagePath = result['croppedImagePath'] as String?;
        if (croppedImagePath != null) {
          _tempAvatarFile.value = File(croppedImagePath);
          _checkForChanges();
          ShowToast.success('头像已设置');
        }
      }
    } catch (e) {
      ShowToast.fail('图片处理失败: ${e.toString()}');
    }
  }
}
