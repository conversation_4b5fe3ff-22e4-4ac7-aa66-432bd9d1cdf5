import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:extended_image/extended_image.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:cheestack_flt/theme.dart';
import '../widgets/unified_option_item.dart';
import '../models/profile_model.dart';
import '../apis/profile_service.dart';

/// Profile 控制器
/// 负责管理用户配置文件页面的状态和业务逻辑，包括显示和编辑功能
class ProfileController extends GetxController {
  static ProfileController get to => Get.find();

  // 服务实例
  final ProfileService _profileService = ProfileService();

  // 表单控制器
  final TextEditingController maxReviewController = TextEditingController();
  final TextEditingController maxStudyController = TextEditingController();
  final TextEditingController introController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  // 密码修改表单控制器
  final TextEditingController currentPasswordController =
      TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  final TextEditingController twoFactorCodeController = TextEditingController();

  // 用户输入模型
  UserInModel userInModel = UserInModel();

  // 编辑模式状态
  bool _isEditMode = false;
  bool get isEditMode => _isEditMode;

  // 临时头像URL（用于即时显示新选择的头像）
  String? _tempAvatarUrl;
  String? get tempAvatarUrl => _tempAvatarUrl;

  // 获取当前显示的头像URL（优先使用临时头像）
  String get currentAvatarUrl {
    final tempUrl = _tempAvatarUrl;
    final userUrl = AuthController.to.usr.user?.avatar ?? '';
    final result = tempUrl ?? userUrl;
    Console.log(
        'currentAvatarUrl: tempUrl=$tempUrl, userUrl=$userUrl, result=$result');
    return result;
  }

  // 密码强度和要求
  PasswordStrength _passwordStrength = PasswordStrength.weak;
  PasswordStrength get passwordStrength => _passwordStrength;

  Map<String, bool> _passwordRequirements = {
    'minLength': false,
    'hasNumber': false,
    'hasLetter': false,
    'hasSpecial': false,
  };
  Map<String, bool> get passwordRequirements => _passwordRequirements;

  @override
  void onInit() {
    super.onInit();
    // 初始化数据
    _initUserModel();
    _setupPasswordValidation();
  }

  /// 初始化用户模型
  void _initUserModel() {
    try {
      final user = AuthController.to.usr.user;
      if (user != null) {
        userInModel = UserInModel(
          username: user.username,
          intro: user.intro,
          avatar: user.avatar,
        );
      }
    } catch (e) {
      // 在测试环境中可能没有 AuthController，使用默认值
      userInModel = UserInModel();
    }
  }

  @override
  void onReady() async {
    super.onReady();
  }

  /// 设置密码验证监听
  void _setupPasswordValidation() {
    newPasswordController.addListener(() {
      final password = newPasswordController.text;
      _passwordStrength = checkPasswordStrength(password);
      _passwordRequirements = validatePasswordRequirements(password);
      update();
    });
  }

  /// 显示底部选择菜单
  void onBottomSheetClick() async {
    final result = await CustomBottomSheet.showSelect(
      items: [
        ...AuthController.to.usernames.map((e) => e).toList(),
        ...UserMenuTab.values.map((e) => e.name).toList(),
      ],
      context: Get.context!,
      builder: (value) {
        return Center(
          child: OxText(value),
        );
      },
    );

    // 根据选择结果执行相应操作
    if (result == UserMenuTab.register.name) {
      await Get.toNamed(AppRoutes.register, arguments: AuthType.register);
    } else if (result == UserMenuTab.login.name) {
      await Get.toNamed(AppRoutes.auth, arguments: AuthType.login);
    } else if (result == UserMenuTab.logout.name) {
      _showLogoutConfirmDialog();
    } else {
      // 切换用户
      if (result.isNotNullOrEmpty) {
        await AuthController.to.switchUser(result ?? "");
      }
    }
  }

  /// 显示主题设置底部弹窗
  void showThemeSettingDialog() async {
    final currentTheme = _profileService.getCurrentThemeMode();

    await Get.bottomSheet(
      _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.palette_outlined,
              title: '主题设置',
              subtitle: '选择您喜欢的主题模式',
            ),
            SizedBox(height: 24.h),

            // 主题选项
            ...AppThemeMode.values.map((mode) {
              return UnifiedOptionItem(
                icon: _getThemeIcon(mode),
                title: mode.name,
                subtitle: mode.description,
                isSelected: mode == currentTheme,
                onTap: () async {
                  Get.back();
                  await _setThemeMode(mode);
                },
              );
            }).toList(),

            SizedBox(height: 16.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.surfaceContainerHighest,
                foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                onPressed: () => Get.back(),
                child: OxText(
                  '取消',
                  fontSize: AppTheme.fontBody,
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }



  /// 获取主题图标
  IconData _getThemeIcon(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.system:
        return Icons.brightness_auto;
      case AppThemeMode.light:
        return Icons.light_mode;
      case AppThemeMode.dark:
        return Icons.dark_mode;
    }
  }

  /// 获取语言图标
  IconData _getLanguageIcon(AppLanguage language) {
    switch (language) {
      case AppLanguage.zh:
        return Icons.translate;
      case AppLanguage.en:
        return Icons.language;
      default:
        return Icons.translate;
    }
  }

  /// 设置主题模式
  Future<void> _setThemeMode(AppThemeMode mode) async {
    try {
      final success = await _profileService.setThemeMode(mode);
      if (success) {
        ShowToast.success('主题设置已更新');
        update(); // 更新UI
      } else {
        ShowToast.text('主题设置失败');
      }
    } catch (e) {
      ShowToast.text('主题设置失败: $e');
    }
  }

  /// 获取当前主题的中文名称
  String getCurrentThemeName() {
    final currentTheme = _profileService.getCurrentThemeMode();
    return currentTheme.name;
  }

  /// 显示语言设置底部弹窗
  void showLanguageSettingDialog() async {
    final currentLanguage = _profileService.getCurrentLanguage();

    await Get.bottomSheet(
      _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.language_outlined,
              title: LocaleKeys.languageSelectTitle.tr,
              subtitle: '选择您的语言偏好',
            ),
            SizedBox(height: 24.h),

            // 语言选项
            ...AppLanguage.values.map((language) {
              return UnifiedOptionItem(
                icon: _getLanguageIcon(language),
                title: language.name,
                subtitle: language.nativeName,
                isSelected: language == currentLanguage,
                onTap: () async {
                  Get.back();
                  await _setLanguage(language);
                },
              );
            }).toList(),

            SizedBox(height: 16.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.surfaceContainerHighest,
                foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                onPressed: () => Get.back(),
                child: OxText(
                  LocaleKeys.cancel.tr,
                  fontSize: AppTheme.fontBody,
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }



  /// 设置语言
  Future<void> _setLanguage(AppLanguage language) async {
    try {
      final success = await _profileService.setLanguage(language);
      if (success) {
        ShowToast.success(LocaleKeys.languageUpdated.tr);
        update(); // 更新UI
      } else {
        ShowToast.text(LocaleKeys.languageUpdateFailed.tr);
      }
    } catch (e) {
      ShowToast.text('${LocaleKeys.languageUpdateFailed.tr}: $e');
    }
  }

  /// 获取当前语言的显示名称
  String getCurrentLanguageName() {
    final currentLanguage = _profileService.getCurrentLanguage();
    return currentLanguage.name;
  }

  /// 修改学习数量
  void onChangeStudyNumber() async {
    await Sdialog.show(
      title: const OxText('5-50之间的数值'),
      confirm: const OxText('确定'),
      cancel: const OxText('取消'),
      context: Get.context!,
      builder: (BuildContext context) {
        return Column(
          children: [
            TextField(
              controller: maxStudyController,
              autofocus: true,
              keyboardType: TextInputType.number,
            ),
          ],
        );
      },
      onConfirm: () async {
        int num = int.parse(maxStudyController.text);
        if (RangeValidator(5, 50).isValid(num) == false) {
          if (num > 50) num = 50;
          if (num < 5) num = 5;
        }
        AuthController.to.usr.config?.studyNumber = num;
        await _updateConfigModel();
        Get.back();
      },
      onCancel: () => Get.back(),
    );
  }

  /// 修改复习数量
  void onChangeReviewNumber() async {
    await Sdialog.show(
      title: const OxText('5-50之间的数值'),
      confirm: const OxText('确定'),
      cancel: const OxText('取消'),
      context: Get.context!,
      builder: (BuildContext context) {
        return Column(
          children: [
            TextField(
              controller: maxReviewController,
              autofocus: true,
              keyboardType: TextInputType.number,
            ),
          ],
        );
      },
      onConfirm: () async {
        int num = int.parse(maxReviewController.text);
        if (RangeValidator(5, 50).isValid(num) == false) {
          if (num > 50) num = 50;
          if (num < 5) num = 5;
        }
        AuthController.to.usr.config?.reviewNumber = num;
        Console.log(AuthController.to.usr.config?.reviewNumber);
        await _updateConfigModel();
        Get.back();
      },
      onCancel: () => Get.back(),
    );
  }

  /// 更新用户配置
  Future<void> _updateConfigModel() async {
    try {
      final updatedConfig = await _profileService.updateUserConfig(
        AuthController.to.usr.config?.toJson() ?? {},
      );
      AuthController.to.usr.config = updatedConfig;
      AuthController.to.update();
    } catch (error) {
      ShowToast.fail(error.toString());
    }
  }

  /// 修改个人简介
  void onChangeIntro() async {
    await Sdialog.show(
      title: const OxText('个人简介'),
      confirm: const OxText('确定'),
      cancel: const OxText('取消'),
      context: Get.context!,
      builder: (BuildContext context) {
        return Column(
          children: [
            TextField(
              controller: introController,
              autofocus: true,
              keyboardType: TextInputType.text,
            ),
          ],
        );
      },
      onConfirm: () async {
        final intro = introController.text;
        if (intro.isNullOrEmpty) {
          ShowToast.fail("请输入个人简介");
          return;
        }
        userInModel.intro = intro;
        await _updateProfileModel();
        Get.back();
      },
      onCancel: () => Get.back(),
    );
  }

  /// 更新用户资料
  Future<void> _updateProfileModel() async {
    try {
      final updatedUser = await _profileService.updateUserProfile(
        userInModel.toJson(),
      );
      AuthController.to.usr.user = updatedUser;
      AuthController.to.updateUsr();
      update();
    } catch (error) {
      ShowToast.fail(error.toString());
    }
  }

  /// 导航到编辑页面
  Future<void> toProfileEdit() async {
    // 导航到新的编辑页面
    final result = await Get.toNamed('/profile/edit');

    // 如果编辑页面返回true，表示有更新，刷新用户信息显示
    if (result == true) {
      update(['userInfo']);
    }
  }

  /// 切换自动播放音频设置
  Future<void> onChangeAutoPlay(bool value) async {
    Console.log(value);
    AuthController.to.usr.config?.isAutoPlayAudio = value;
    await AuthController.to.updateConfigModel();
    update();
  }

  /// 切换AI音频自动播放设置
  Future<void> onChangeAIPlay(bool value) async {
    Console.log(value);
    AuthController.to.usr.config?.isAutoPlayAiAudio = value;
    await AuthController.to.updateConfigModel();
    update();
  }

  // ==================== 编辑功能方法 ====================

  /// 切换编辑模式
  void toggleEditMode() {
    _isEditMode = !_isEditMode;
    update();
  }

  /// 进入编辑模式
  void enterEditMode() {
    _isEditMode = true;
    // 初始化编辑控制器的值
    try {
      usernameController.text = AuthController.to.usr.user?.username ?? '';
      introController.text = AuthController.to.usr.user?.intro ?? '';
    } catch (e) {
      // 在测试环境中可能没有 AuthController，忽略错误
      Console.log('AuthController not available in test environment: $e');
    }
    update();
  }

  /// 退出编辑模式
  void exitEditMode() {
    _isEditMode = false;
    // 清空控制器
    usernameController.clear();
    introController.clear();
    passwordController.clear();
    // 清除临时头像
    _tempAvatarUrl = null;
    update(['avatar']);
  }

  /// 清除临时头像
  void clearTempAvatar() {
    _tempAvatarUrl = null;
    update(['avatar']);
  }

  /// 保存用户信息
  Future<void> saveProfile({
    String? username,
    String? intro,
    String? avatar,
    bool showSuccessToast = true,
    bool exitEdit = true,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (username != null) updateData['username'] = username;
      if (intro != null) updateData['intro'] = intro;
      if (avatar != null && avatar.isNotEmpty) updateData['avatar'] = avatar;

      if (updateData.isEmpty) {
        if (showSuccessToast) ShowToast.fail('没有需要更新的内容');
        return;
      }

      Console.log('更新用户数据: $updateData');
      final updatedUser = await _profileService.updateUserProfile(updateData);

      // 更新本地用户数据
      AuthController.to.usr.user = updatedUser;
      AuthController.to.updateUsr();

      if (showSuccessToast) ShowToast.success('保存成功');
      if (exitEdit) exitEditMode();

      // 根据更新内容刷新对应组件
      if (avatar != null) {
        update(['avatar']);
      }
      if (username != null || intro != null) {
        update(['userInfo']); // 刷新用户信息显示
      }
    } catch (error) {
      Console.log('保存失败: $error');
      if (showSuccessToast) ShowToast.fail('保存失败: ${error.toString()}');
    }
  }

  /// 上传用户头像 - 直接上传到COS
  Future<void> uploadAvatar() async {
    try {
      final file = await chvImagePicker(source: ImageSource.gallery);
      if (file != null) {
        // 创建一个持久的临时文件副本，避免原文件被删除
        final tempDir = Directory.systemTemp;
        final tempFile = File(
            '${tempDir.path}/temp_avatar_${DateTime.now().millisecondsSinceEpoch}.jpg');
        await file.copy(tempFile.path);

        // 立即设置临时头像URL，使用持久的临时文件路径
        _tempAvatarUrl = tempFile.path;
        Console.log('设置临时头像: $_tempAvatarUrl');
        Console.log('文件是否存在: ${await tempFile.exists()}');
        update(['avatar']); // 只刷新头像组件

        ShowToast.loading(val: '正在上传头像...');

        // 生成COS路径
        final cosPath = OxCos().getKey(
          path: file.path,
          type: UploadFileType.image,
          filename: 'avatar_${DateTime.now().millisecondsSinceEpoch}',
        );

        // 直接上传到COS
        final uploadSuccess = await OxCos().upload(
          srcPath: file.path,
          cosPath: cosPath,
          onSuccess: (result, cosXmlResult) {
            Console.log('头像上传成功: $result');
          },
          onFail: (clientException, serviceException) {
            Console.log('头像上传失败: $clientException, $serviceException');
          },
        );

        if (uploadSuccess) {
          // 构建完整的头像URL
          final avatarUrl = '${OxCos().baseUrl}$cosPath';
          Console.log('生成的头像URL: $avatarUrl');

          // 删除旧头像
          final oldAvatar = AuthController.to.usr.user?.avatar;
          if (oldAvatar != null && oldAvatar.isNotEmpty) {
            Console.log('删除旧头像: $oldAvatar');
            await OxCos().delete(oldAvatar);
          }

          // 先清除缓存，再更新数据
          try {
            clearMemoryImageCache();
            await clearDiskCachedImages();
            if (oldAvatar != null && oldAvatar.isNotEmpty) {
              await clearDiskCachedImage(oldAvatar);
            }
            Console.log('缓存已清除');
          } catch (e) {
            Console.log('清除缓存失败: $e');
          }

          // 立即更新本地用户数据
          AuthController.to.usr.user?.avatar = avatarUrl;
          AuthController.to.updateUsr();

          // 清理临时文件
          if (_tempAvatarUrl != null) {
            try {
              await File(_tempAvatarUrl!).delete();
              Console.log('临时文件已删除: $_tempAvatarUrl');
            } catch (e) {
              Console.log('删除临时文件失败: $e');
            }
          }

          // 清除临时头像，设置正式头像URL
          _tempAvatarUrl = null;

          // 通过API更新用户头像信息（不显示成功提示，不退出编辑模式）
          await saveProfile(
            avatar: avatarUrl,
            showSuccessToast: false,
            exitEdit: false,
          );

          // 清除图片缓存，确保新头像立即显示
          try {
            // 清除ExtendedImage的缓存
            clearMemoryImageCache();
            await clearDiskCachedImages();

            // 清除特定URL的缓存
            if (oldAvatar != null && oldAvatar.isNotEmpty) {
              await clearDiskCachedImage(oldAvatar);
            }
            await clearDiskCachedImage(avatarUrl);
          } catch (e) {
            Console.log('清除图片缓存失败: $e');
          }

          // 强制刷新头像UI
          update(['avatar']);

          ShowToast.dismiss();
          ShowToast.success('头像更新成功');
        } else {
          // 上传失败，清理临时文件
          if (_tempAvatarUrl != null) {
            try {
              await File(_tempAvatarUrl!).delete();
              Console.log('临时文件已删除: $_tempAvatarUrl');
            } catch (e) {
              Console.log('删除临时文件失败: $e');
            }
          }

          // 清除临时头像
          _tempAvatarUrl = null;
          update(['avatar']);

          ShowToast.dismiss();
          ShowToast.fail('头像上传失败');
        }
      }
    } catch (error) {
      // 出错时清理临时文件
      if (_tempAvatarUrl != null) {
        try {
          await File(_tempAvatarUrl!).delete();
          Console.log('临时文件已删除: $_tempAvatarUrl');
        } catch (e) {
          Console.log('删除临时文件失败: $e');
        }
      }

      // 清除临时头像
      _tempAvatarUrl = null;
      update(['avatar']);

      ShowToast.dismiss();
      ShowToast.fail('头像上传失败: ${error.toString()}');
      Console.log('头像上传错误: $error');
    }
  }

  /// 删除用户账户
  Future<bool> deleteAccount({required String password}) async {
    try {
      final success = await _profileService.deleteAccount();
      if (success) {
        await AuthController.to.logout();
        Get.offAllNamed(AppRoutes.splash);
        ShowToast.success('账户删除成功');
        return true;
      } else {
        ShowToast.fail('密码错误或删除失败');
        return false;
      }
    } catch (error) {
      ShowToast.fail('删除账户失败: ${error.toString()}');
      return false;
    }
  }

  /// 验证密码强度
  static PasswordStrength checkPasswordStrength(String password) {
    if (password.length < 6) {
      return PasswordStrength.weak;
    }

    int score = 0;

    // 检查长度
    if (password.length >= 6) score++;
    if (password.length >= 8) score++;

    // 检查字符类型
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;

    if (score <= 2) {
      return PasswordStrength.weak;
    } else if (score <= 4) {
      return PasswordStrength.medium;
    } else {
      return PasswordStrength.strong;
    }
  }

  /// 验证密码要求
  static Map<String, bool> validatePasswordRequirements(String password) {
    return {
      'minLength': password.length >= 6,
      'hasNumber': RegExp(r'[0-9]').hasMatch(password),
      'hasLetter': RegExp(r'[a-zA-Z]').hasMatch(password),
      'hasSpecial': RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password),
    };
  }

  /// 获取密码强度文本
  String getPasswordStrengthText() {
    switch (_passwordStrength) {
      case PasswordStrength.weak:
        return LocaleKeys.securityPasswordWeak.tr;
      case PasswordStrength.medium:
        return LocaleKeys.securityPasswordMedium.tr;
      case PasswordStrength.strong:
        return LocaleKeys.securityPasswordStrong.tr;
    }
  }

  /// 获取密码强度颜色
  Color getPasswordStrengthColor() {
    switch (_passwordStrength) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.green;
    }
  }

  /// 修改密码
  Future<void> changePassword() async {
    if (!_validatePasswordForm()) return;

    try {
      final request = ChangePasswordRequest(
        currentPassword: currentPasswordController.text,
        newPassword: newPasswordController.text,
      );

      final success = await _profileService.changePassword(request);
      if (success) {
        ShowToast.success(LocaleKeys.securityPasswordChanged.tr);
        _clearPasswordForm();
        Get.back();
      }
    } catch (e) {
      ShowToast.fail(
          '${LocaleKeys.securityChangePassword.tr}${LocaleKeys.errorNetwork.tr}: $e');
    }
  }

  /// 验证密码表单
  bool _validatePasswordForm() {
    if (currentPasswordController.text.isEmpty) {
      ShowToast.fail(
          LocaleKeys.securityCurrentPassword.tr + LocaleKeys.formRequired.tr);
      return false;
    }

    if (newPasswordController.text.isEmpty) {
      ShowToast.fail(
          LocaleKeys.securityNewPassword.tr + LocaleKeys.formRequired.tr);
      return false;
    }

    if (confirmPasswordController.text.isEmpty) {
      ShowToast.fail(
          LocaleKeys.securityConfirmPassword.tr + LocaleKeys.formRequired.tr);
      return false;
    }

    if (newPasswordController.text != confirmPasswordController.text) {
      ShowToast.fail(LocaleKeys.securityPasswordMismatch.tr);
      return false;
    }

    if (_passwordStrength == PasswordStrength.weak) {
      ShowToast.fail('密码强度太弱，请设置更强的密码');
      return false;
    }

    return true;
  }

  /// 清空密码表单
  void _clearPasswordForm() {
    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
  }

  /// 显示删除账户确认底部弹窗
  void showDeleteAccountDialog() {
    Get.bottomSheet(
      _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.delete_forever_outlined,
              title: '删除账户',
              subtitle: '即将删除你的帐号，该操作不可逆，是否确认删除？',
              iconColor: Get.theme.colorScheme.error,
            ),
            SizedBox(height: 32.h),

            // 确认删除按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.error,
                foregroundColor: Get.theme.colorScheme.onError,
                onPressed: () {
                  Get.back();
                  _showPasswordConfirmDialog();
                },
                child: OxText(
                  '确认删除',
                  fontSize: AppTheme.fontBody,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 12.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.surfaceContainerHighest,
                foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                onPressed: () => Get.back(),
                child: OxText(
                  '取消',
                  fontSize: AppTheme.fontBody,
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }

  /// 显示密码确认底部弹窗
  void _showPasswordConfirmDialog() {
    Get.bottomSheet(
      _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.lock_outline,
              title: '请输入密码确认',
              subtitle: '为了安全起见，请输入您的密码以确认删除操作',
            ),
            SizedBox(height: 24.h),

            // 密码输入框
            OxInputField(
              hintText: '请输入密码',
              obscureText: true,
              controller: passwordController,
            ),
            SizedBox(height: 24.h),

            // 确认按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.error,
                foregroundColor: Get.theme.colorScheme.onError,
                onPressed: () async {
                  if (passwordController.text.isEmpty) {
                    ShowToast.fail('请输入密码');
                    return;
                  }
                  Get.back();
                  await deleteAccount(password: passwordController.text);
                },
                child: OxText(
                  '确认删除',
                  fontSize: AppTheme.fontBody,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 12.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.surfaceContainerHighest,
                foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                onPressed: () => Get.back(),
                child: OxText(
                  '取消',
                  fontSize: AppTheme.fontBody,
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }

  @override
  void onClose() {
    maxReviewController.dispose();
    maxStudyController.dispose();
    introController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    twoFactorCodeController.dispose();
    super.onClose();
  }

  /// 统一的底部弹窗构建方法
  Widget _buildUnifiedBottomSheet({
    required Widget child,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部指示器
              Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.onSurfaceVariant
                      .withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              SizedBox(height: 24.h),
              child,
            ],
          ),
        ),
      ),
    );
  }

  /// 统一的弹窗标题构建方法
  Widget _buildBottomSheetHeader({
    required IconData icon,
    required String title,
    String? subtitle,
    Color? iconColor,
  }) {
    return Column(
      children: [
        // 标题行
        Row(
          children: [
            Icon(
              icon,
              size: 28.w,
              color: iconColor ?? Get.theme.colorScheme.primary,
            ),
            SizedBox(width: 12.w),
            OxText(
              title,
              fontSize: AppTheme.fontTitle,
              fontWeight: FontWeight.bold,
            ),
          ],
        ),
        if (subtitle != null) ...[
          SizedBox(height: 8.h),
          // 描述文字
          Align(
            alignment: Alignment.centerLeft,
            child: OxText(
              subtitle,
              fontSize: AppTheme.fontBody,
              color: Get.theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  /// 显示退出登录确认底部弹窗
  void _showLogoutConfirmDialog() {
    Get.bottomSheet(
      _buildUnifiedBottomSheet(
        child: Column(
          children: [
            // 标题和描述
            _buildBottomSheetHeader(
              icon: Icons.logout_outlined,
              title: LocaleKeys.authLogout.tr,
              subtitle: LocaleKeys.accountLogoutConfirm.tr,
              iconColor: Get.theme.colorScheme.error,
            ),
            SizedBox(height: 32.h),

            // 确认按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.error,
                foregroundColor: Get.theme.colorScheme.onError,
                onPressed: () async {
                  Get.back();
                  await AuthController.to.logout();
                  ShowToast.success(LocaleKeys.accountLogoutSuccess.tr);
                },
                child: OxText(
                  LocaleKeys.authLogout.tr,
                  fontSize: AppTheme.fontBody,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 12.h),

            // 取消按钮
            SizedBox(
              width: double.infinity,
              child: Sbutton(
                backgroundColor: Get.theme.colorScheme.surfaceContainerHighest,
                foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
                onPressed: () => Get.back(),
                child: OxText(
                  LocaleKeys.cancel.tr,
                  fontSize: AppTheme.fontBody,
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }
}

/// 密码强度枚举
enum PasswordStrength { weak, medium, strong }

/// 修改密码请求模型
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;

  ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
  });

  Map<String, dynamic> toJson() => {
        'current_password': currentPassword,
        'new_password': newPassword,
      };
}
