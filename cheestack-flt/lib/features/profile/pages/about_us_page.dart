import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:flutter/services.dart';
import 'package:cheestack_flt/features/profile/controllers/about_us_page_controller.dart';

class AboutUsPage extends GetView<AboutUsController> {
  const AboutUsPage({super.key});

  Widget _buildView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          _buildLogo(),
          Sized<PERSON>ox(height: 32.h),
          _buildAppInfo(),
          SizedBox(height: 40.h),
          _buildContactSection(),
          SizedBox(height: 40.h),
          _buildAgreements(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// LOGO
  Widget _buildLogo() {
    return Container(
      width: 120.w,
      height: 120.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: Theme.of(Get.context!)
                .colorScheme
                .shadow
                .withValues(alpha: 0.1),
            blurRadius: 20.r,
            offset: Offset(0, 8.h),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.r),
        child: const Simage(
          url: 'assets/images/logo.png',
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  /// 应用信息
  Widget _buildAppInfo() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Theme.of(Get.context!).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color:
              Theme.of(Get.context!).colorScheme.outline.withValues(alpha: 0.1),
          width: 1.w,
        ),
      ),
      child: Column(
        children: [
          OxText(
            LocaleKeys.aboutUsAppName.tr,
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: Theme.of(Get.context!).colorScheme.onSurface,
          ),
          SizedBox(height: 12.h),
          OxText(
            LocaleKeys.aboutUsAppDescription.tr,
            fontSize: 16.sp,
            color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            textAlign: TextAlign.center,
            maxLines: 3,
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: Theme.of(Get.context!).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: OxText(
              "${LocaleKeys.aboutUsVersion.tr}：${controller.version}",
              fontSize: 14.sp,
              color: Theme.of(Get.context!).colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 联系方式区域
  Widget _buildContactSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Theme.of(Get.context!).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color:
              Theme.of(Get.context!).colorScheme.outline.withValues(alpha: 0.1),
          width: 1.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.contact_support_outlined,
                size: 24.w,
                color: Theme.of(Get.context!).colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              OxText(
                LocaleKeys.aboutUsContact.tr,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: Theme.of(Get.context!).colorScheme.onSurface,
              ),
            ],
          ),
          SizedBox(height: 8.h),
          OxText(
            LocaleKeys.aboutUsContactDesc.tr,
            fontSize: 14.sp,
            color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 20.h),
          _buildContactItem(
            icon: Icons.wechat_outlined,
            label: LocaleKeys.aboutUsContactWechat.tr,
            value: 'Scott_Lyu',
            onTap: () => _copyToClipboard('Scott_Lyu', '微信号'),
          ),
          SizedBox(height: 16.h),
          _buildContactItem(
            icon: Icons.chat_outlined,
            label: LocaleKeys.aboutUsContactQQ.tr,
            value: '63758890',
            onTap: () => _copyToClipboard('63758890', 'QQ号'),
          ),
          SizedBox(height: 16.h),
          _buildContactItem(
            icon: Icons.email_outlined,
            label: LocaleKeys.aboutUsContactEmail.tr,
            value: '<EMAIL>',
            onTap: () => _copyToClipboard('<EMAIL>', '邮箱地址'),
          ),
        ],
      ),
    );
  }

  /// 联系方式项目
  Widget _buildContactItem({
    required IconData icon,
    required String label,
    required String value,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Theme.of(Get.context!)
                .colorScheme
                .outline
                .withValues(alpha: 0.2),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(Get.context!).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(Get.context!).colorScheme.onPrimaryContainer,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  OxText(
                    label,
                    fontSize: 14.sp,
                    color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(height: 2.h),
                  OxText(
                    value,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(Get.context!).colorScheme.onSurface,
                  ),
                ],
              ),
            ),
            Icon(
              Icons.copy_outlined,
              size: 18.w,
              color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text, String type) {
    Clipboard.setData(ClipboardData(text: text));
    ShowToast.success('$type 已复制到剪贴板');
  }

  /// 协议
  Widget _buildAgreements() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Theme.of(Get.context!).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color:
              Theme.of(Get.context!).colorScheme.outline.withValues(alpha: 0.1),
          width: 1.w,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.description_outlined,
                size: 20.w,
                color: Theme.of(Get.context!).colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              OxText(
                LocaleKeys.aboutUsAgreements.tr,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(Get.context!).colorScheme.onSurface,
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAgreementButton(
                text: LocaleKeys.aboutUsUserAgreement.tr,
                onTap: () => controller.showAgreementDialog(),
              ),
              Container(
                width: 1.w,
                height: 20.h,
                color: Theme.of(Get.context!)
                    .colorScheme
                    .outline
                    .withValues(alpha: 0.3),
              ),
              _buildAgreementButton(
                text: LocaleKeys.aboutUsPrivacyPolicy.tr,
                onTap: () => controller.showPrivacyDialog(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 协议按钮
  Widget _buildAgreementButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: OxText(
          text,
          fontSize: 14.sp,
          color: Theme.of(Get.context!).colorScheme.primary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AboutUsController>(
      init: AboutUsController(),
      builder: (_) {
        return Scaffold(
          appBar: OxAppBar(
            title: OxText(
              LocaleKeys.aboutUsTitle.tr,
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          body: SafeArea(
            child: _buildView(),
          ),
        );
      },
    );
  }
}
