import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/theme.dart';

/// Profile 设置项组件
/// 用于显示设置项的通用组件
class ProfileSettingsCell extends StatelessWidget {
  final Widget? icon;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final bool showArrow;
  final VoidCallback? onTap;

  const ProfileSettingsCell({
    Key? key,
    this.icon,
    required this.title,
    this.subtitle,
    this.trailing,
    this.showArrow = true,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: AppTheme.paddingMedium,
        child: Row(
          children: [
            // 图标
            if (icon != null) ...[
              icon!,
              SizedBox(width: 12.w),
            ],
            // 标题和副标题
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  OxText(
                    title,
                    fontSize: AppTheme.fontBody,
                    color: Theme.of(Get.context!).colorScheme.onSurface,
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: 2.h),
                    OxText(
                      subtitle!,
                      fontSize: AppTheme.fontSmall,
                      color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                    ),
                  ],
                ],
              ),
            ),
            // 尾部组件
            if (trailing != null) ...[
              SizedBox(width: 8.w),
              trailing!,
            ],
            // 箭头
            if (showArrow) ...[
              SizedBox(width: 8.w),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.w,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Profile 开关设置项组件
/// 带有开关的设置项
class ProfileSwitchCell extends StatelessWidget {
  final Widget? icon;
  final String title;
  final String? subtitle;
  final bool value;
  final ValueChanged<bool>? onChanged;

  const ProfileSwitchCell({
    Key? key,
    this.icon,
    required this.title,
    this.subtitle,
    required this.value,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: AppTheme.paddingMedium,
      child: Row(
        children: [
          // 图标
          if (icon != null) ...[
            icon!,
            SizedBox(width: 12.w),
          ],
          // 标题和副标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                OxText(
                  title,
                  fontSize: AppTheme.fontBody,
                  color: Theme.of(Get.context!).colorScheme.onSurface,
                ),
                if (subtitle != null) ...[
                  SizedBox(height: 2.h),
                  OxText(
                    subtitle!,
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                  ),
                ],
              ],
            ),
          ),
          // 开关
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(Get.context!).colorScheme.primary,
          ),
        ],
      ),
    );
  }
}
