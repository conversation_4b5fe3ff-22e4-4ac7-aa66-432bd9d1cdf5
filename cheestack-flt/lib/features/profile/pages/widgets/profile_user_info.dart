import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/theme.dart';

/// Profile 用户信息组件
/// 显示用户头像、姓名、手机号和简介信息
class ProfileUserInfo extends StatelessWidget {
  final String avatar;
  final String? name;
  final String? mobile;
  final String? intro;
  final VoidCallback? onTap;

  const ProfileUserInfo({
    Key? key,
    required this.avatar,
    this.name,
    this.mobile,
    this.intro,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: AppTheme.paddingMedium,
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            // 用户头像
            OxAvatar.medium(
              url: avatar,
              shape: OxAvatarShape.circle,
            ),
            SizedBox(width: 16.w),
            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 用户名
                  if (name != null)
                    OxText(
                      name!,
                      fontSize: AppTheme.fontLarge,
                      fontWeight: FontWeight.bold,
                      color:
                          Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                    ),
                  SizedBox(height: 4.h),
                  // 手机号
                  if (mobile != null)
                    OxText(
                      mobile!,
                      fontSize: AppTheme.fontBody,
                      color:
                          Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                    ),
                  SizedBox(height: 4.h),
                  // 个人简介
                  if (intro != null && intro!.isNotEmpty)
                    OxText(
                      intro!,
                      fontSize: AppTheme.fontSmall,
                      color:
                          Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            // 编辑图标
            Icon(
              Icons.edit,
              size: 20.w,
              color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }
}
