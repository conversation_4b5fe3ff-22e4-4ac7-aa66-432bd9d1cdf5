import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:extended_image/extended_image.dart';
import '../controllers/image_crop_controller.dart';

/// 图片裁剪页面
/// 提供正方形裁剪功能，支持旋转、翻转等基本编辑操作
class ImageCropPage extends StatelessWidget {
  const ImageCropPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据主题选择背景颜色
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDark ? Colors.black : Theme.of(context).colorScheme.surface;

    return GetBuilder<ImageCropController>(
      init: ImageCropController(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: backgroundColor,
          appBar: _buildAppBar(context, controller),
          body: _buildBody(context, controller),
          bottomNavigationBar: _buildBottomBar(context, controller),
        );
      },
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar(
      BuildContext context, ImageCropController controller) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final appBarColor =
        isDark ? Colors.black : Theme.of(context).colorScheme.surface;
    final textColor =
        isDark ? Colors.white : Theme.of(context).colorScheme.onSurface;

    return AppBar(
      backgroundColor: appBarColor,
      foregroundColor: textColor,
      title: const Text('裁剪图片'),
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: controller.cancelCrop,
      ),
      actions: [
        TextButton(
          onPressed: controller.isLoading ? null : controller.confirmCrop,
          child: controller.isLoading
              ? SizedBox(
                  width: 20.w,
                  height: 20.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: textColor,
                  ),
                )
              : Text(
                  '完成',
                  style: TextStyle(
                    color: textColor,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ],
    );
  }

  /// 构建页面主体
  Widget _buildBody(BuildContext context, ImageCropController controller) {
    if (controller.originalImageFile == null) {
      final textColor = Theme.of(context).brightness == Brightness.dark
          ? Colors.white
          : Theme.of(context).colorScheme.onSurface;

      return Center(
        child: Text(
          '没有可编辑的图片',
          style: TextStyle(
            color: textColor,
            fontSize: 16.sp,
          ),
        ),
      );
    }

    // 根据主题获取合适的边框颜色
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final borderColor =
        isDark ? Colors.white : Theme.of(context).colorScheme.primary;

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: ExtendedImage.file(
        controller.originalImageFile!,
        mode: ExtendedImageMode.editor,
        fit: BoxFit.contain,
        extendedImageEditorKey: controller.editorKey,
        initEditorConfigHandler: (state) {
          return EditorConfig(
            maxScale: 8.0,
            cropRectPadding: EdgeInsets.all(20.w),
            hitTestSize: 20.w,
            cropAspectRatio: 1.0, // 强制正方形裁剪
            initCropRectType: InitCropRectType.imageRect,
            cropLayerPainter: const EditorCropLayerPainter(),
            cornerSize: Size(30.w, 5.w),
            cornerColor: borderColor,
            lineColor: borderColor.withValues(alpha: 0.8),
            lineHeight: 2.w,
          );
        },
        cacheRawData: true,
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(BuildContext context, ImageCropController controller) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final bottomBarColor =
        isDark ? Colors.black : Theme.of(context).colorScheme.surface;

    return SafeArea(
      child: Container(
        color: bottomBarColor,
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 6.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              context: context,
              icon: Icons.crop_square,
              label: '正方形',
              onTap: controller.resetCropToSquare,
            ),
            _buildActionButton(
              context: context,
              icon: Icons.rotate_right,
              label: '旋转',
              onTap: controller.rotateImage,
            ),
            _buildActionButton(
              context: context,
              icon: Icons.flip,
              label: '翻转',
              onTap: controller.flipImage,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final iconColor =
        isDark ? Colors.white : Theme.of(context).colorScheme.onSurface;
    final backgroundColor = isDark
        ? Colors.white.withValues(alpha: 0.1)
        : Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 36.w,
              height: 36.w,
              decoration: BoxDecoration(
                color: backgroundColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 18.sp,
                color: iconColor,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              label,
              style: TextStyle(
                fontSize: 9.sp,
                color: iconColor.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
