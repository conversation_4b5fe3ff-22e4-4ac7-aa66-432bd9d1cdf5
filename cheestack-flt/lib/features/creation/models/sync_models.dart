/// 同步状态枚举
enum SyncStatus {
  /// 空闲状态
  idle,
  /// 同步中
  syncing,
  /// 同步成功
  success,
  /// 同步失败
  failed,
}

/// 同步状态数据模型
class SyncState {
  /// 同步状态
  final SyncStatus status;
  
  /// 同步进度 (0.0 - 1.0)
  final double progress;
  
  /// 状态消息（通常用于错误信息）
  final String? message;
  
  /// 最后同步时间
  final DateTime? lastSyncTime;

  const SyncState({
    required this.status,
    this.progress = 0.0,
    this.message,
    this.lastSyncTime,
  });

  /// 复制并更新部分字段
  SyncState copyWith({
    SyncStatus? status,
    double? progress,
    String? message,
    DateTime? lastSyncTime,
  }) {
    return SyncState(
      status: status ?? this.status,
      progress: progress ?? this.progress,
      message: message ?? this.message,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
    );
  }

  @override
  String toString() {
    return 'SyncState(status: $status, progress: $progress, message: $message, lastSyncTime: $lastSyncTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SyncState &&
        other.status == status &&
        other.progress == progress &&
        other.message == message &&
        other.lastSyncTime == lastSyncTime;
  }

  @override
  int get hashCode {
    return status.hashCode ^
        progress.hashCode ^
        message.hashCode ^
        lastSyncTime.hashCode;
  }
}
