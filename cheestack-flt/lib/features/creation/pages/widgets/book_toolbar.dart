import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import '../../controllers/creation_controller.dart';
import 'book_filter_bar.dart';

/// 书籍工具栏组件 - 包含排序、视图切换、结果统计
class BookToolbar extends StatelessWidget {
  final CreationController controller;

  const BookToolbar({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          _buildResultCount(context),
          const Spacer(),
          _buildSyncButton(context),
          Sized<PERSON>ox(width: 8.w),
          _buildSortButton(context),
          <PERSON><PERSON><PERSON><PERSON>(width: 8.w),
          _buildViewToggle(context),
        ],
      ),
    );
  }

  /// 构建结果统计
  Widget _buildResultCount(BuildContext context) {
    final count = controller.filteredBookList.length;
    return OxText(
      '共 $count 本书籍',
      fontSize: AppTheme.fontSmall,
      color: Theme.of(context).colorScheme.onSurfaceVariant,
    );
  }

  /// 构建同步按钮
  Widget _buildSyncButton(BuildContext context) {
    return GestureDetector(
      onTap: controller.isSyncing ? null : () => controller.syncBooksOnly(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: _getSyncButtonColor(context),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: _getSyncBorderColor(context),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (controller.isSyncing)
              SizedBox(
                width: 14.sp,
                height: 14.sp,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              )
            else
              Icon(
                _getSyncIcon(),
                size: 16.sp,
                color: _getSyncIconColor(context),
              ),
            SizedBox(width: 4.w),
            OxText(
              _getSyncButtonText(),
              fontSize: AppTheme.fontSmall,
              color: _getSyncTextColor(context),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取同步按钮颜色
  Color _getSyncButtonColor(BuildContext context) {
    if (controller.isSyncing) {
      return Theme.of(context).colorScheme.surfaceContainerHighest;
    }
    switch (controller.syncStatus) {
      case 'success':
        return Theme.of(context).colorScheme.primaryContainer;
      case 'failed':
        return Theme.of(context).colorScheme.errorContainer;
      default:
        return Theme.of(context).colorScheme.surfaceContainerHighest;
    }
  }

  /// 获取同步按钮边框颜色
  Color _getSyncBorderColor(BuildContext context) {
    if (controller.isSyncing) {
      return Theme.of(context).colorScheme.outline.withValues(alpha: 0.3);
    }
    switch (controller.syncStatus) {
      case 'success':
        return Theme.of(context).colorScheme.primary.withValues(alpha: 0.5);
      case 'failed':
        return Theme.of(context).colorScheme.error.withValues(alpha: 0.5);
      default:
        return Theme.of(context).colorScheme.outline.withValues(alpha: 0.3);
    }
  }

  /// 获取同步图标
  IconData _getSyncIcon() {
    switch (controller.syncStatus) {
      case 'success':
        return Icons.check_circle_outline;
      case 'failed':
        return Icons.error_outline;
      default:
        return Icons.sync;
    }
  }

  /// 获取同步图标颜色
  Color _getSyncIconColor(BuildContext context) {
    switch (controller.syncStatus) {
      case 'success':
        return Theme.of(context).colorScheme.primary;
      case 'failed':
        return Theme.of(context).colorScheme.error;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// 获取同步文本颜色
  Color _getSyncTextColor(BuildContext context) {
    switch (controller.syncStatus) {
      case 'success':
        return Theme.of(context).colorScheme.primary;
      case 'failed':
        return Theme.of(context).colorScheme.error;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// 获取同步按钮文本
  String _getSyncButtonText() {
    if (controller.isSyncing) {
      return '同步中';
    }
    switch (controller.syncStatus) {
      case 'success':
        return '已同步';
      case 'failed':
        return '同步失败';
      default:
        return '同步';
    }
  }

  /// 构建排序按钮
  Widget _buildSortButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _showSortOptions(context),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 4.w),
            OxText(
              controller.getSortOptionText(controller.selectedSortOption),
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建视图切换按钮
  Widget _buildViewToggle(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildViewButton(
            context,
            icon: Icons.view_list,
            isSelected: !controller.isGridView,
            onTap: () {
              if (controller.isGridView) {
                controller.toggleViewMode();
              }
            },
          ),
          _buildViewButton(
            context,
            icon: Icons.grid_view,
            isSelected: controller.isGridView,
            onTap: () {
              if (!controller.isGridView) {
                controller.toggleViewMode();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(
    BuildContext context, {
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Icon(
          icon,
          size: 16.sp,
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  /// 显示排序选项
  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _SortOptionsSheet(controller: controller),
    );
  }
}

/// 排序选项底部弹窗
class _SortOptionsSheet extends StatelessWidget {
  final CreationController controller;

  const _SortOptionsSheet({required this.controller});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Container(
          padding: AppTheme.paddingMedium,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              ..._buildSortOptions(context, ctrl),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        OxText(
          '排序方式',
          fontSize: AppTheme.fontTitle,
          fontWeight: FontWeight.bold,
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(
            Icons.close,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSortOptions(
      BuildContext context, CreationController ctrl) {
    final sortOptions = [
      {
        'value': 'created_desc',
        'label': '创建时间（最新优先）',
        'icon': Icons.access_time
      },
      {
        'value': 'created_asc',
        'label': '创建时间（最早优先）',
        'icon': Icons.access_time
      },
      {'value': 'updated_desc', 'label': '更新时间（最新优先）', 'icon': Icons.update},
      {'value': 'updated_asc', 'label': '更新时间（最早优先）', 'icon': Icons.update},
      {'value': 'name_asc', 'label': '名称（A-Z）', 'icon': Icons.sort_by_alpha},
      {'value': 'name_desc', 'label': '名称（Z-A）', 'icon': Icons.sort_by_alpha},
    ];

    return sortOptions.map((option) {
      final isSelected = ctrl.selectedSortOption == option['value'];
      return ListTile(
        leading: Icon(
          option['icon'] as IconData,
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        title: OxText(
          option['label'] as String,
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface,
        ),
        trailing: isSelected
            ? Icon(
                Icons.check,
                color: Theme.of(context).colorScheme.primary,
              )
            : null,
        onTap: () {
          ctrl.setSortOption(option['value'] as String);
          Get.back();
        },
      );
    }).toList();
  }
}

/// 搜索栏和筛选器组合组件
class BookSearchAndFilter extends StatelessWidget {
  final CreationController controller;

  const BookSearchAndFilter({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: AppTheme.paddingMedium,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 48.h,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: Theme.of(context)
                      .colorScheme
                      .outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: TextField(
                onChanged: controller.onBookSearchChanged,
                decoration: InputDecoration(
                  hintText: '搜索书籍名称、简介...',
                  hintStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: AppTheme.fontSmall,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20.sp,
                  ),
                  suffixIcon: controller.bookSearchKeyword.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                            size: 20.sp,
                          ),
                          onPressed: () => controller.onBookSearchChanged(''),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 12.h,
                  ),
                ),
                style: TextStyle(
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          GestureDetector(
            onTap: () => _showFilterPanel(context),
            child: Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: controller.isFilterActive
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: controller.isFilterActive
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                ),
              ),
              child: Icon(
                Icons.tune,
                color: controller.isFilterActive
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterPanel(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BookFilterPanel(controller: controller),
    );
  }
}
