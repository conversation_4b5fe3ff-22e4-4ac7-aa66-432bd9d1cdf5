import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/shared/utils/refresh_controller_factory.dart';

import '../controllers/creation_controller.dart';
import 'widgets/book_filter_bar.dart';
import 'widgets/book_card.dart';
import 'widgets/book_toolbar.dart';

/// 书籍管理列表页面
class BookListPage extends StatefulWidget {
  const BookListPage({super.key});

  @override
  State<BookListPage> createState() => _BookListPageState();
}

class _BookListPageState extends State<BookListPage>
    with AutomaticKeepAliveClientMixin {
  late CreationController controller;
  late RefreshController refreshController;
  late String _controllerKey;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _controllerKey = 'book_list_${hashCode}';
    refreshController = RefreshControllerFactory.create(key: _controllerKey);
    if (!Get.isRegistered<CreationController>()) {
      Get.put<CreationController>(CreationController());
    }
    controller = Get.find<CreationController>();
    controller.loadBookList();
  }

  @override
  void dispose() {
    RefreshControllerFactory.dispose(_controllerKey);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Scaffold(
          appBar: _buildAppBar(ctrl),
          body: Column(
            children: [
              BookFilterBar(
                controller: ctrl,
                isVisible: ctrl.isFilterActive,
              ),
              BookToolbar(controller: ctrl),
              _buildSyncStatusBar(ctrl),
              Expanded(child: _buildView(ctrl)),
            ],
          ),
          floatingActionButton: _buildFloatingActionButton(ctrl),
        );
      },
    );
  }

  AppBar _buildAppBar(CreationController ctrl) {
    return AppBar(
      title: Row(
        children: [
          Expanded(
            child: Container(
              height: 40.h,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: Theme.of(context)
                      .colorScheme
                      .outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: TextField(
                onChanged: ctrl.onBookSearchChanged,
                decoration: InputDecoration(
                  hintText: '搜索书籍名称、简介...',
                  hintStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: AppTheme.fontSmall,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20.sp,
                  ),
                  suffixIcon: ctrl.bookSearchKeyword.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                            size: 18.sp,
                          ),
                          onPressed: () => ctrl.onBookSearchChanged(''),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 8.h,
                  ),
                ),
                style: TextStyle(
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          GestureDetector(
            onTap: () => _showFilterPanel(context, ctrl),
            child: Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: ctrl.isFilterActive
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: ctrl.isFilterActive
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                ),
              ),
              child: Icon(
                Icons.tune,
                color: ctrl.isFilterActive
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建同步状态栏
  Widget _buildSyncStatusBar(CreationController ctrl) {
    // 只在同步进行中或有错误时显示
    if (!ctrl.isSyncing && ctrl.syncStatus != 'failed') {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: ctrl.isSyncing || ctrl.syncStatus == 'failed' ? 48.h : 0,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: _getSyncStatusBarColor(ctrl),
          border: Border(
            bottom: BorderSide(
              color:
                  Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            if (ctrl.isSyncing)
              SizedBox(
                width: 20.sp,
                height: 20.sp,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              )
            else
              Icon(
                Icons.error_outline,
                size: 20.sp,
                color: Theme.of(context).colorScheme.error,
              ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OxText(
                    _getSyncStatusText(ctrl),
                    fontSize: AppTheme.fontSmall,
                    color: _getSyncStatusTextColor(ctrl),
                    fontWeight: FontWeight.w500,
                  ),
                  if (ctrl.syncErrorMessage != null &&
                      ctrl.syncStatus == 'failed')
                    OxText(
                      ctrl.syncErrorMessage!,
                      fontSize: AppTheme.fontSmall,
                      color: Theme.of(context)
                          .colorScheme
                          .error
                          .withValues(alpha: 0.8),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            if (ctrl.syncStatus == 'failed')
              GestureDetector(
                onTap: () => ctrl.syncBooksOnly(),
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: OxText(
                    '重试',
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(context).colorScheme.onError,
                  ),
                ),
              ),
            if (ctrl.isSyncing || ctrl.syncStatus == 'failed')
              SizedBox(width: 8.w),
            GestureDetector(
              onTap: () => ctrl.resetSyncStatus(),
              child: Icon(
                Icons.close,
                size: 18.sp,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取同步状态栏颜色
  Color _getSyncStatusBarColor(CreationController ctrl) {
    if (ctrl.isSyncing) {
      return Theme.of(context)
          .colorScheme
          .primaryContainer
          .withValues(alpha: 0.3);
    }
    if (ctrl.syncStatus == 'failed') {
      return Theme.of(context)
          .colorScheme
          .errorContainer
          .withValues(alpha: 0.3);
    }
    return Theme.of(context).colorScheme.surface;
  }

  /// 获取同步状态文本
  String _getSyncStatusText(CreationController ctrl) {
    if (ctrl.isSyncing) {
      return '正在同步书籍数据...';
    }
    if (ctrl.syncStatus == 'failed') {
      return '同步失败';
    }
    return '';
  }

  /// 获取同步状态文本颜色
  Color _getSyncStatusTextColor(CreationController ctrl) {
    if (ctrl.isSyncing) {
      return Theme.of(context).colorScheme.primary;
    }
    if (ctrl.syncStatus == 'failed') {
      return Theme.of(context).colorScheme.error;
    }
    return Theme.of(context).colorScheme.onSurface;
  }

  Widget _buildView(CreationController ctrl) {
    if (ctrl.filteredBookList.isEmpty) {
      return ctrl.bookList.isEmpty ? _buildEmptyView() : _buildNoResultsView();
    }
    return _buildBookList(ctrl);
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 64.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          OxText(
            '还没有书籍',
            fontSize: AppTheme.fontTitle,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          OxText(
            '点击右下角按钮创建您的第一本书籍',
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          OxText(
            '没有找到匹配的书籍',
            fontSize: AppTheme.fontTitle,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          OxText(
            '尝试调整筛选条件或搜索关键词',
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookList(CreationController ctrl) {
    return SmartRefresher(
      controller: refreshController,
      onRefresh: _onRefresh,
      onLoading: _onLoadMore,
      enablePullUp: true,
      enablePullDown: true,
      child: ctrl.isGridView ? _buildGridView(ctrl) : _buildListView(ctrl),
    );
  }

  Widget _buildListView(CreationController ctrl) {
    return ListView.builder(
      padding: AppTheme.paddingMedium,
      itemCount: ctrl.filteredBookList.length,
      itemBuilder: (context, index) {
        final book = ctrl.filteredBookList[index];
        return BookListCard(
          book: book,
          controller: ctrl,
        );
      },
    );
  }

  Widget _buildGridView(CreationController ctrl) {
    return GridView.builder(
      padding: AppTheme.paddingMedium,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getGridCrossAxisCount(),
        childAspectRatio: 0.75,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: ctrl.filteredBookList.length,
      itemBuilder: (context, index) {
        final book = ctrl.filteredBookList[index];
        return BookGridCard(
          book: book,
          controller: ctrl,
        );
      },
    );
  }

  int _getGridCrossAxisCount() {
    final screenWidth = 1.sw;
    if (screenWidth > 600.w) {
      return 3; // 平板设备显示3列
    } else {
      return 2; // 手机设备显示2列
    }
  }

  Future<void> _onRefresh() async {
    try {
      await controller.loadBookList();
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  Future<void> _onLoadMore() async {
    try {
      final moreBooks = await controller.loadMoreBooks();

      if (moreBooks.isEmpty) {
        refreshController.loadNoData();
      } else {
        controller.bookList.addAll(moreBooks);
        controller.update();
        refreshController.loadComplete();
      }
    } catch (e) {
      refreshController.loadFailed();
    }
  }

  Widget _buildFloatingActionButton(CreationController ctrl) {
    return FloatingActionButton.extended(
      onPressed: () => ctrl.createBook(),
      icon: const Icon(Icons.add),
      label: const OxText('创建书籍'),
    );
  }

  void _showFilterPanel(BuildContext context, CreationController ctrl) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BookFilterPanel(controller: ctrl),
    );
  }
}
