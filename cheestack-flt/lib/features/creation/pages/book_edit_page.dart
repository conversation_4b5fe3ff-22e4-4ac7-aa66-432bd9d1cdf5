import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';

import '../controllers/book_controller.dart';

/// 书籍编辑页面
class BookEditPage extends StatefulWidget {
  const BookEditPage({super.key});

  @override
  State<BookEditPage> createState() => _BookEditPageState();
}

class _BookEditPageState extends State<BookEditPage> {
  late BookController controller;

  @override
  void initState() {
    super.initState();
    Get.put(BookController());
    controller = Get.find<BookController>();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BookController>(
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: _buildView(),
        );
      },
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: OxText(controller.isCreate ? '创建书籍' : '编辑书籍'),
      centerTitle: true,
      actions: [
        // 调试按钮
        if (controller.isCreate)
          IconButton(
            onPressed: () => controller.testBookCreation(),
            icon: const Icon(Icons.bug_report),
            tooltip: '测试创建',
          ),
        TextButton(
          onPressed: controller.isLoading ? null : controller.saveBook,
          child: controller.isLoading
              ? SizedBox(
                  width: 20.w,
                  height: 20.h,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                )
              : const OxText('保存'),
        ),
      ],
    );
  }

  Widget _buildView() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCoverSection(),
          _buildBasicInfoSection(),
          _buildPrivacySection(),
        ],
      ),
    );
  }

  Widget _buildCoverSection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            OxText(
              '书籍封面',
              fontSize: AppTheme.fontTitle,
              fontWeight: FontWeight.bold,
            ),
            Center(
              child: GestureDetector(
                onTap: controller.selectCover,
                child: Container(
                  width: 120.w,
                  height: 160.h,
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline,
                      style: BorderStyle.solid,
                    ),
                  ),
                  child: controller.coverImage != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12.r),
                          child: Simage(
                            url: controller.coverImage!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_photo_alternate_outlined,
                              size: 32.sp,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                            OxText(
                              '添加封面',
                              fontSize: AppTheme.fontSmall,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ],
                        ),
                ),
              ),
            ),
            if (controller.coverImage != null) ...[
              Center(
                child: TextButton.icon(
                  onPressed: controller.removeCover,
                  icon: const Icon(Icons.delete_outline),
                  label: const OxText('移除封面'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            OxText(
              '基本信息',
              fontSize: AppTheme.fontTitle,
              fontWeight: FontWeight.bold,
            ),
            SizedBox(height: 16.h),
            TextFormField(
              controller: controller.nameController,
              decoration: InputDecoration(
                labelText: '书籍名称',
                hintText: '请输入书籍名称',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入书籍名称';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            TextFormField(
              controller: controller.briefController,
              maxLines: 3,
              decoration: InputDecoration(
                labelText: '书籍简介',
                hintText: '请输入书籍简介（可选）',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySection() {
    return Card(
      child: Padding(
        padding: AppTheme.paddingMedium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            OxText(
              '隐私设置',
              fontSize: AppTheme.fontTitle,
              fontWeight: FontWeight.bold,
            ),
            SizedBox(height: 16.h),
            // 横向排列的隐私选项
            Row(
              children: [
                Expanded(
                    child: _buildPrivacyOption('free', '公开', Icons.public)),
                SizedBox(width: 8.w),
                Expanded(
                    child: _buildPrivacyOption('private', '私有', Icons.lock)),
                SizedBox(width: 8.w),
                Expanded(
                    child: _buildPrivacyOption(
                        'paid', '收费', Icons.monetization_on)),
                SizedBox(width: 8.w),
                Expanded(
                    child:
                        _buildPrivacyOption('member_free', '会员', Icons.star)),
              ],
            ),
            SizedBox(height: 12.h),
            // 显示当前选中选项的说明
            _buildPrivacyDescription(),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyOption(String value, String label, IconData icon) {
    final isSelected = controller.privacy == value;
    return GestureDetector(
      onTap: () => controller.setPrivacy(value),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 24.sp,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 4.h),
            OxText(
              label,
              fontSize: AppTheme.fontSmall,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyDescription() {
    String description;
    switch (controller.privacy) {
      case 'free':
        description = '所有人都可以查看和学习此书籍的内容';
        break;
      case 'private':
        description = '只有您作为作者才能查看和编辑此书籍';
        break;
      case 'paid':
        description = '用户需要购买后才能查看此书籍的内容';
        break;
      case 'member_free':
        description = '只有开通了APP会员的用户才能查看此书籍';
        break;
      default:
        description = '请选择隐私设置';
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHigh,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16.sp,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: OxText(
              description,
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
