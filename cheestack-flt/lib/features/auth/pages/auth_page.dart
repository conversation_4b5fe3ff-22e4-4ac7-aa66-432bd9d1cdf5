import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/features/auth/pages/widgets/unified_input_field.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// 认证页面主视图
/// 根据当前页面类型显示不同的认证页面
class AuthPage extends StatelessWidget {
  const AuthPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text('手机号一键登录'),
      ),
      body: SafeArea(
        minimum: EdgeInsets.all(32.w),
        child: GetBuilder<AuthController>(
          builder: (controller) {
            return SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height -
                      MediaQuery.of(context).viewPadding.top -
                      MediaQuery.of(context).viewPadding.bottom -
                      kToolbarHeight - // AppBar高度
                      64.w, // SafeArea的内边距
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      SizedBox(height: 20.h),
                      // 顶部导航栏
                      _buildLogoSection(context),
                      SizedBox(height: 36.h),
                      // 手机号输入区域
                      _buildPhoneInputSection(controller, context),
                      SizedBox(height: 24.h),
                      // 获取验证码按钮
                      UnifiedButton(
                        text: '获取验证码',
                        onPressed: () => controller.sendVerificationCode(),
                        isLoading: controller.isSendingCode,
                      ),
                      SizedBox(height: 16.h),
                      // 切换到密码登录的文本按钮
                      _buildSwitchToPasswordButton(controller, context),
                      const Spacer(flex: 1),
                      // 协议勾选区域
                      _buildAgreementSection(controller, context),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建LOGO区域
  Widget _buildLogoSection(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 120.w,
          height: 120.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: Image.asset(
              "assets/images/logo.png",
              width: 120.w,
              height: 120.w,
              fit: BoxFit.cover,
            ),
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          '你的芝士堆',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  /// 构建手机号输入区域
  Widget _buildPhoneInputSection(
      AuthController controller, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 手机号输入框
        Form(
          key: controller.phoneFormKey,
          child: PhoneInputField(
            controller: controller.phoneController,
            countryCode: controller.countryCode,
            onCountryCodeTap: () => _showCountryCodePicker(controller, context),
            validator: controller.validatePhone,
          ),
        ),
        SizedBox(height: 12.h),
        // 提示信息
        Padding(
          padding: REdgeInsets.only(left: 16),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 14.sp,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 6.w),
              Text(
                '未注册用户会自动注册',
                style: TextStyle(
                  fontSize: 13.sp,
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建切换到密码登录的文本按钮
  Widget _buildSwitchToPasswordButton(
      AuthController controller, BuildContext context) {
    return TextButton(
      onPressed: () => controller.goToPasswordLogin(),
      child: Text(
        '使用账号密码登录',
        style: TextStyle(
          fontSize: 14.sp,
          color: Theme.of(context).colorScheme.primary,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }

  /// 构建协议勾选区域
  Widget _buildAgreementSection(
      AuthController controller, BuildContext context) {
    return Row(
      children: [
        Obx(() {
          return Checkbox(
            value: controller.isAgreed,
            onChanged: (value) => controller.toggleAgreement(),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
            activeColor: Theme.of(context).colorScheme.primary,
          );
        }),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: Theme.of(context).textTheme.titleSmall?.fontSize,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              children: [
                const TextSpan(text: '已阅读并同意'),
                TextSpan(
                  text: '《服务协议》',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () => _showServiceAgreement(context),
                ),
                const TextSpan(text: '和'),
                TextSpan(
                  text: '《隐私政策》',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () => _showPrivacyPolicy(context),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 显示国家代码选择器
  void _showCountryCodePicker(AuthController controller, BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          height: 300.h,
          padding: REdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                '选择国家/地区',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 16.h),
              Expanded(
                child: ListView(
                  children: [
                    _buildCountryCodeItem(controller, context, '+86', '中国'),
                    _buildCountryCodeItem(controller, context, '+1', '美国'),
                    _buildCountryCodeItem(controller, context, '+44', '英国'),
                    _buildCountryCodeItem(controller, context, '+81', '日本'),
                    _buildCountryCodeItem(controller, context, '+82', '韩国'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建国家代码选项
  Widget _buildCountryCodeItem(AuthController controller, BuildContext context,
      String code, String country) {
    return ListTile(
      title: Text(
        '$country ($code)',
        style: TextStyle(
          fontSize: 16.sp,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
      onTap: () {
        controller.setCountryCode(code);
        Get.back();
      },
    );
  }

  /// 显示服务协议
  void _showServiceAgreement(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: REdgeInsets.all(20),
          child: Column(
            children: [
              // 标题
              Text(
                '服务协议',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 16.h),
              // 可滚动内容
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    '欢迎使用CheeStack服务！\n\n'
                    '1. 服务条款\n'
                    '本服务协议是您与CheeStack之间关于使用CheeStack服务的法律协议。使用本服务即表示您同意遵守本协议的所有条款和条件。\n\n'
                    '2. 用户权利与义务\n'
                    '用户有权使用本应用提供的各项功能，包括但不限于信息浏览、数据存储、内容分享等。同时，用户需要遵守相关法律法规，不得利用本服务从事违法违规活动。\n\n'
                    '3. 隐私保护\n'
                    '我们承诺保护用户的个人信息安全，采用行业标准的安全措施保护用户数据，不会未经授权泄露用户隐私信息。具体内容请参阅我们的隐私政策。\n\n'
                    '4. 知识产权\n'
                    '本应用及其包含的所有内容（包括但不限于文字、图片、音频、视频、软件等）的知识产权归CheeStack或相关权利人所有。\n\n'
                    '5. 服务变更与中断\n'
                    '我们保留随时修改、暂停或终止服务的权利，并会提前通知用户。因系统维护、升级或其他原因导致的服务中断，我们将尽力减少影响。\n\n'
                    '6. 免责声明\n'
                    '本应用仅提供信息服务平台，对于因使用本服务而产生的任何直接或间接损失不承担责任。用户应自行承担使用风险。\n\n'
                    '7. 争议解决\n'
                    '因本协议产生的争议，双方应友好协商解决；协商不成的，可向有管辖权的人民法院提起诉讼。\n\n'
                    '8. 协议修改\n'
                    '我们保留随时修改本协议的权利，修改后的协议将在应用内公布。继续使用服务即视为同意修改后的协议。\n\n'
                    '9. 其他条款\n'
                    '本协议的解释权归CheeStack所有。如有疑问，请联系我们的客服团队。',
                    style: TextStyle(
                      fontSize: 14.sp,
                      height: 1.6,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              // 按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('我知道了'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示隐私政策
  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: REdgeInsets.all(20),
          child: Column(
            children: [
              // 标题
              Text(
                '隐私政策',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 16.h),
              // 可滚动内容
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    'CheeStack隐私政策\n\n'
                    '最后更新日期：2024年\n\n'
                    '1. 信息收集\n'
                    '我们可能收集以下类型的信息：\n'
                    '• 个人身份信息：手机号码、用户名等\n'
                    '• 设备信息：设备型号、操作系统版本、设备标识符\n'
                    '• 使用信息：应用使用情况、功能偏好、操作日志\n'
                    '• 位置信息：在您授权的情况下收集位置数据\n\n'
                    '2. 信息使用\n'
                    '我们收集的信息用于以下目的：\n'
                    '• 提供和维护我们的服务\n'
                    '• 改善用户体验和服务质量\n'
                    '• 发送重要通知和更新\n'
                    '• 进行安全监控和欺诈防范\n'
                    '• 遵守法律法规要求\n\n'
                    '3. 信息保护\n'
                    '我们采用以下措施保护您的个人信息：\n'
                    '• 数据加密传输和存储\n'
                    '• 访问权限控制和身份验证\n'
                    '• 定期安全审计和漏洞修复\n'
                    '• 员工隐私培训和保密协议\n\n'
                    '4. 信息共享\n'
                    '除以下情况外，我们不会与第三方共享您的个人信息：\n'
                    '• 获得您的明确同意\n'
                    '• 法律法规要求或政府部门要求\n'
                    '• 保护我们的合法权益\n'
                    '• 紧急情况下保护用户安全\n\n'
                    '5. Cookie和类似技术\n'
                    '我们可能使用Cookie和类似技术来：\n'
                    '• 记住您的偏好设置\n'
                    '• 分析应用使用情况\n'
                    '• 提供个性化服务\n'
                    '您可以通过设备设置管理Cookie偏好。\n\n'
                    '6. 您的权利\n'
                    '您对个人信息享有以下权利：\n'
                    '• 访问和查看您的个人信息\n'
                    '• 更正不准确的信息\n'
                    '• 删除个人信息\n'
                    '• 限制信息处理\n'
                    '• 数据可携带权\n\n'
                    '7. 儿童隐私\n'
                    '我们不会故意收集13岁以下儿童的个人信息。如发现此类情况，我们将立即删除相关信息。\n\n'
                    '8. 政策更新\n'
                    '本隐私政策可能会定期更新。重大变更时，我们会通过应用内通知或其他方式告知您。\n\n'
                    '9. 联系我们\n'
                    '如对本隐私政策有任何疑问，请通过应用内反馈功能联系我们。',
                    style: TextStyle(
                      fontSize: 14.sp,
                      height: 1.6,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              // 按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('我知道了'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
