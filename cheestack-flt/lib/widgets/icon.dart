part of widgets;

Widget mOnOffIcon(
  IconData iconData,
  Function()? onTap, {
  bool isOn = false,
  Color? offColor,
  Color? onColor,
}) {
  return InkWell(
    onTap: onTap,
    child: Icon(
      iconData,
      color: isOn
          ? onColor ?? Theme.of(Get.context!).colorScheme.primary
          : offColor ?? Theme.of(Get.context!).colorScheme.onSurface,
    ),
  );
}

IconButton mIconButtonCommon({
  Icon? icon,
  double? iconSize,
  Color? color,
  Function()? onPressed,
}) {
  return IconButton(
    icon: icon ?? const Icon(Icons.add),
    iconSize: iconSize ?? 24,
    onPressed: onPressed,
    padding: EdgeInsets.zero,
    color: color,
  );
}

Widget mIconButtonactivatable({
  IconData iconData = Icons.add,
  double iconSize = 40,
  Color defaultColor = Colors.grey,
  Color? activedColor,
  bool isActivated = false,
  Function()? onPressed,
}) {
  activedColor = activedColor ?? Theme.of(Get.context!).colorScheme.primary;
  Color color = isActivated ? activedColor : defaultColor;
  return IconButton(
    icon: Icon(iconData),
    iconSize: iconSize,
    onPressed: onPressed,
    padding: EdgeInsets.zero,
    color: color,
  );
}

class SswitchItem {
  Widget item;

  /// onTap will be called when icon is clicked
  Function()? onTap;
  SswitchItem({required this.item, required this.onTap});
}

class SswitchWrapper extends StatefulWidget {
  final int index;
  final List<SswitchItem> items;

  const SswitchWrapper({
    Key? key,
    required this.index,
    required this.items,
  }) : super(key: key);

  @override
  State<SswitchWrapper> createState() => _SswitchWrapperState();
}

class _SswitchWrapperState extends State<SswitchWrapper> {
  int curretnIndex = 0;

  @override
  void initState() {
    curretnIndex = widget.index;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant SswitchWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        widget.items[curretnIndex].onTap?.call();
        setState(() {
          if (curretnIndex + 1 >= widget.items.length) {
            curretnIndex = 0;
          } else {
            curretnIndex = (curretnIndex + 1);
          }
        });
      },
      child: widget.items[curretnIndex].item,
    );
  }
}

class SinwellIconButton extends StatefulWidget {
  final String? iconText;
  final IconData iconData;
  final Color? color;
  final Color? backGroundColor;
  final double? width;
  final double? height;
  final double? iconSize;
  final Function()? onTab;

  const SinwellIconButton({
    super.key,
    this.iconText,
    this.iconData = Icons.add,
    this.color,
    this.backGroundColor,
    this.width,
    this.height,
    this.iconSize,
    this.onTab,
  });

  @override
  State<SinwellIconButton> createState() => _SinwellIconButtonState();
}

class _SinwellIconButtonState extends State<SinwellIconButton> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTab,
      child: Container(
        width: widget.width ?? iconTextContainerWidth,
        height: widget.height ?? iconTextContainerHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
              (widget.width ?? iconTextContainerWidth) / 10),
          color: widget.backGroundColor ?? Colors.grey[200],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.iconData,
              size: iconTextSize,
              color: widget.color,
            ),
            widget.iconText == null
                ? const SizedBox()
                : Text(widget.iconText ?? ""),
          ],
        ),
      ),
    );
  }
}

// /// 一个icon在上,文字在下的矩形组件, 有点击事件
// Widget sinwellIconButton({
//   String? iconText,
//   IconData iconData = Icons.add,
//   Color? color,
//   Color? backGroundColor,
//   double? width,
//   double? height,
//   double? iconSize,
//   Function()? onTab,
// }) {
//   return InkWell(
//     onTap: onTab,
//     child: Container(
//       // margin: EdgeInsets.all(MARGIN_LARGE_NUM),
//       width: width ?? iconTextContainerWidth,
//       height: height ?? iconTextContainerHeight,
//       decoration: BoxDecoration(
//         borderRadius:
//             BorderRadius.circular(width ?? iconTextContainerWidth / 10),
//         color: backGroundColor ?? Colors.grey[100],
//       ),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(
//             iconData,
//             size: iconTextSize,
//             color: color,
//           ),
//           iconText == null ? const SizedBox() : Text(iconText),
//         ],
//       ),
//     ),
//   );
// }
