part of widgets;



/// 自定义表单输入框
class OxFormField extends FormField<String> {
  /// 文本控制器, 用于控制输入框的内容
  final TextEditingController? controller;

  /// 标签, 用于显示在输入框上方
  final String? label;

  /// 提示信息, 用于显示在输入框下方
  final String? helper;

  /// 提示信息, 用于显示在输入框里面
  final String? hintText;

  /// 是否必填, 默认为`false`, 为`true`时, 会在输入框后面显示一个红色的星号
  /// 且输入框为空时, 会显示一个红色的提示信息
  /// 且输入框为空时, 表单验证会返回一个错误信息
  final bool required;

  /// 是否只读, 默认为`false`
  /// 为`true`时, 输入框不可编辑
  /// 为`false`时, 输入框可编辑
  final bool readOnly;

  /// 是否隐藏输入内容, 默认为`false`
  /// 为`true`时, 输入内容会被隐藏, 会在输入框后面显示一个眼睛图标, 一般用于密码输入框
  /// 为`false`时, 输入内容不会被隐藏, 一般用于普通输入框
  final bool obscureText;

  /// 是否自动获取焦点, 默认为`false`, 为`true`时, 输入框会自动获取焦点
  final bool autofocus;

  /// 输入框的图标, 位于输入框的右侧
  final IconData? iconData;

  /// 输入框的最大行数, 默认为`1`
  /// 一般用于多行输入框, 用于控制输入框的高度
  final int? maxLines;

  /// 输入框的点击事件, 一般用于弹出键盘
  final void Function()? onTap;

  /// 输入框的图标点击事件, 例如用于显示密码
  final void Function()? onIcon;

  /// 输入框的内容改变事件, 一般用于实时获取输入框的内容, 例如用于实时验证输入框的内容
  /// 例如: `onChanged: (value) => print(value)`
  final ValueChanged<String>? onChanged;

  /// 输入框的键盘类型, 一般用于控制输入框的键盘类型, 例如用于控制输入框的键盘类型为数字键盘
  final TextInputType? keyboardType;

  /// 输入框的输入格式, 一般用于控制输入框的输入格式, 例如用于控制输入框的输入格式为手机号码
  final List<TextInputFormatter>? inputFormatters;

  /// 表单验证器, 一般用于控制输入框的输入格式, 例如用于控制输入框的输入格式为手机号码
  OxFormField({
    Key? key,
    this.controller,
    this.label,
    this.helper,
    this.hintText,
    this.required = false,
    this.readOnly = false,
    this.obscureText = false,
    this.autofocus = false,
    this.maxLines = 1,
    this.onTap,
    this.onIcon,
    this.iconData,
    this.onChanged,
    this.keyboardType,
    this.inputFormatters,
    Validator<String?>? validator,
  }) : super(
          key: key,
          validator: (value) {
            if (required) {
              final call = RequiredValidator().call(value);
              if (validator == null) return call;
              return call ?? validator.call(value);
            }
            return validator?.call(value);
          },
          builder: (field) {
            final state = field as _CustomFormInputState;
            final decoration = const InputDecoration().applyDefaults(
              Theme.of(field.context).inputDecorationTheme,
            );
            return UnmanagedRestorationScope(
              bucket: field.bucket,
              child: OxInputField(
                controller: state._effectiveController,
                label: label,
                hintText: hintText,
                required: required,
                readOnly: readOnly,
                obscureText: obscureText,
                autofocus: autofocus,
                onTap: onTap,
                onIcon: onIcon,
                iconData: iconData,
                maxLines: maxLines,
                error: field.errorText,
                decoration: decoration,
                inputFormatters: inputFormatters,
                keyboardType: keyboardType,
                onChanged: (value) {
                  field.didChange(value);
                  onChanged?.call(value);
                },
              ),
            );
          },
        );

  @override
  FormFieldState<String> createState() => _CustomFormInputState();
}

class _CustomFormInputState extends FormFieldState<String> {
  RestorableTextEditingController? _controller;

  TextEditingController get _effectiveController =>
      widget.controller ?? _controller!.value;

  @override
  OxFormField get widget => super.widget as OxFormField;

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    super.restoreState(oldBucket, initialRestore);
    if (_controller != null) _registerController();
    setValue(_effectiveController.text);
  }

  void _registerController() {
    assert(_controller != null);
    registerForRestoration(_controller!, 'controller');
  }

  void _createLocalController([TextEditingValue? value]) {
    assert(_controller == null);
    _controller = value == null
        ? RestorableTextEditingController()
        : RestorableTextEditingController.fromValue(value);
    if (!restorePending) _registerController();
  }

  @override
  void initState() {
    super.initState();
    if (widget.controller == null) {
      final editingValue = widget.initialValue != null
          ? TextEditingValue(text: widget.initialValue!)
          : null;
      _createLocalController(editingValue);
    } else {
      widget.controller!.addListener(_onControllerChanged);
    }
  }

  @override
  void didUpdateWidget(OxFormField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller?.removeListener(_onControllerChanged);
      widget.controller?.addListener(_onControllerChanged);

      if (oldWidget.controller != null && widget.controller == null) {
        _createLocalController(oldWidget.controller!.value);
      }

      if (widget.controller != null) {
        setValue(widget.controller!.text);
        if (oldWidget.controller == null) {
          unregisterFromRestoration(_controller!);
          _controller!.dispose();
          _controller = null;
        }
      }
    }
  }

  @override
  void dispose() {
    widget.controller?.removeListener(_onControllerChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChange(String? value) {
    super.didChange(value);
    if (_effectiveController.text != value) {
      _effectiveController.text = value ?? '';
    }
  }

  @override
  void reset() {
    _effectiveController.text = widget.initialValue ?? '';
    super.reset();
  }

  void _onControllerChanged() {
    if (_effectiveController.text != value) {
      didChange(_effectiveController.text);
    }
  }
}

class OxInputField extends StatelessWidget {
  final TextEditingController? controller;
  final String? label;
  final String? helper;
  final String? hintText;
  final String? error;
  final bool required;
  final bool readOnly;
  final bool obscureText;
  final IconData? iconData;
  final int? minLines;
  final int? maxLines;
  final void Function()? onTap;
  final void Function()? onIcon;
  final ValueChanged<String>? onChanged;
  final InputDecoration decoration;
  final TextInputType? keyboardType;
  final bool autofocus;
  final List<TextInputFormatter>? inputFormatters;

  const OxInputField({
    Key? key,

    /// 文本控制器, 用于控制输入框的内容
    this.controller,
    this.label,
    this.helper,
    this.hintText,
    this.error,
    this.required = false,
    this.readOnly = false,
    this.obscureText = false,
    this.onTap,
    this.onIcon,
    this.iconData,
    this.onChanged,
    this.decoration = const InputDecoration(),
    this.minLines,
    this.maxLines = 1,
    this.keyboardType,
    this.autofocus = false,
    this.inputFormatters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final decorationTheme = theme.inputDecorationTheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (label != null || required)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              // horizontal: (decorationTheme.contentPadding?.horizontal ?? 0) / 2,
              vertical: 8,
            ).copyWith(top: 0),
            child: RichText(
              text: TextSpan(
                text: label,
                style: decorationTheme.labelStyle,
                children: [
                  if (required)
                    TextSpan(
                      text: label != null ? ' *' : '*',
                      style: const TextStyle(color: AppTheme.error),
                    ),
                ],
              ),
            ),
          ),
        TextField(
            autofocus: autofocus,
            controller: controller,
            readOnly: readOnly,
            onTap: onTap,
            obscureText: obscureText,
            onChanged: onChanged,
            minLines: minLines,
            maxLines: maxLines,
            inputFormatters: inputFormatters,
            keyboardType: keyboardType,
            style: TextStyle(
              fontFamily: 'Sans',
              fontSize: FontSize.body,
              height: 1.3,
              // fontWeight: FontWeight.w600,
            ),
            decoration: decoration.copyWith(
              hintText: hintText,
              hintStyle: const TextStyle(fontWeight: FontWeight.normal),
              suffixIconConstraints: const BoxConstraints(),
            suffixIcon: _suffixIcon(decorationTheme),
          ),
        ),
        AnimatedSize(
          duration: const Duration(milliseconds: 180),
          alignment: Alignment.topCenter,
          child: error != null
              ? Padding(
                  padding: EdgeInsets.only(top: 10.w),
                  child: CustomAlert.error(
                    size: CustomAlertSize.mini,
                    text: Text(error!),
                  ),
                )
              : const SizedBox.shrink(),
        ),
        if (helper != null)
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: (decorationTheme.contentPadding?.horizontal ?? 0) / 2,
              vertical: 10.w,
            ).copyWith(bottom: 0),
            child: Text(
              helper!,
              style: decorationTheme.helperStyle,
            ),
          )
      ],
    );
  }

  Widget? _suffixIcon(InputDecorationTheme decorationTheme) {
    if (iconData == null) return null;
    return Padding(
      padding: EdgeInsetsDirectional.only(
        end: (decorationTheme.contentPadding?.horizontal ?? 0) / 2,
      ),
      child: GestureDetector(
        onTap: onIcon,
        child: Icon(iconData!, size: 24.w),
      ),
    );
  }
}

class SinputSearch extends StatelessWidget {
  /// 搜索图标, 位于输入框左侧
  final IconData iconData;

  /// 输入框提示文本
  final String? hintText;

  /// 输入框控制器
  final TextEditingController? controller;

  /// 是否只读
  final bool readOnly;

  /// onChange回调, 一般用于搜索框内容变化时, 通知父组件更新搜索结果
  final void Function(String)? onChanged;

  /// 点击输入框回调, 一般用于跳转到搜索页面或者弹出搜索框和键盘
  final void Function()? onTap;

  /// autofocus
  final bool autofocus;
  const SinputSearch({
    Key? key,
    this.iconData = Icons.search_rounded,
    this.hintText,
    this.controller,
    this.readOnly = false,
    this.onTap,
    this.onChanged,
    this.autofocus = false,
  }) : super(key: key);

  EdgeInsetsGeometry get _padding {
    return EdgeInsets.symmetric(
      vertical: 13.w,
      horizontal: 20.w,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final inputDecoration = theme.inputDecorationTheme;
    
    return TextField(
      autofocus: autofocus,
      readOnly: readOnly,
      controller: controller,
      onChanged: onChanged,
      onTap: onTap,
      style: theme.textTheme.bodyMedium?.copyWith(
        height: 1.3,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        contentPadding: _padding.subtract(EdgeInsets.symmetric(
          horizontal: _padding.horizontal / 4,
        )),
        prefixIcon: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: _padding.horizontal / 4,
          ),
          child: Icon(
            iconData,
            size: IconSize.body,
            color: colorScheme.primary,
          ),
        ),
        prefixIconConstraints: const BoxConstraints(),
        hintText: hintText ?? LocaleKeys.formSearch.tr,
        hintStyle: theme.inputDecorationTheme.hintStyle,
        // 使用主题中定义的边框样式
        border: inputDecoration.border,
        enabledBorder: inputDecoration.enabledBorder,
        focusedBorder: inputDecoration.focusedBorder,
        errorBorder: inputDecoration.errorBorder,
        focusedErrorBorder: inputDecoration.focusedErrorBorder,
      ),
    );
  }
}

/// 验证码输入框
class CustomInputCode extends StatefulWidget {
  /// 验证码位数
  final int? count;

  /// 是否可用
  final bool enabled;

  /// 错误提示
  final String? errorText;

  /// 验证器
  final FormFieldValidator<String>? validator;

  /// 完成回调
  final void Function(GlobalKey<FormState> key, String value)? onCompleted;

  const CustomInputCode({
    Key? key,
    this.count,
    this.onCompleted,
    this.validator,
    this.enabled = true,
    this.errorText,
  }) : super(key: key);

  @override
  State<CustomInputCode> createState() => _CustomInputCodeState();
}

class _CustomInputCodeState extends State<CustomInputCode> {
  final _formKey = GlobalKey<FormState>();

  int get _count => widget.count ?? 4;

  PinTheme _defaultTheme(ThemeData theme) {
    return PinTheme(
      width: double.infinity,
      height: 68.w,
      padding: const EdgeInsets.all(0),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      textStyle: theme.textTheme.bodyMedium?.copyWith(
        fontSize: 30.w,
        fontWeight: FontWeight.w600,
        height: 1.3,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.all(Radius.circular(20.w)),
        border: theme.inputDecorationTheme.border != null
            ? Border.fromBorderSide(
                theme.inputDecorationTheme.border!.borderSide,
              )
            : null,
      ),
    );
  }

  PinTheme _focusedTheme(ThemeData theme) {
    if (theme.inputDecorationTheme.border == null) return _defaultTheme(theme);
    return _defaultTheme(theme).copyBorderWith(
      border: Border.fromBorderSide(
        theme.inputDecorationTheme.focusedBorder!.borderSide,
      ),
    );
  }

  PinTheme _errorTheme(ThemeData theme) {
    if (theme.inputDecorationTheme.border == null) return _defaultTheme(theme);
    return _defaultTheme(theme).copyBorderWith(
      border: Border.fromBorderSide(
        theme.inputDecorationTheme.errorBorder!.borderSide,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Form(
      key: _formKey,
      child: Pinput(
        length: _count,
        autofocus: true,
        defaultPinTheme: _defaultTheme(theme),
        focusedPinTheme: _focusedTheme(theme),
        errorPinTheme: _errorTheme(theme),
        errorText: widget.errorText,
        enabled: widget.enabled,
        showCursor: false,
        pinputAutovalidateMode: PinputAutovalidateMode.disabled,
        errorBuilder: (error, value) {
          return Container(
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(
              horizontal: (_defaultTheme(theme).margin?.horizontal ?? 0) / 2,
            ).copyWith(top: 10.w),
            child: CustomAlert.error(
              size: CustomAlertSize.mini,
              text: Text(error ?? 'Error'),
            ),
          );
        },
        validator: widget.validator,
        onCompleted: (value) => widget.onCompleted?.call(_formKey, value),
      ),
    );
  }
}

class CtFormInput extends FormField<String> {
  CtFormInput({
    super.key,
    String? initialValue,
    FormFieldValidator<String>? validator,
  }) : super(
          initialValue: initialValue,
          validator: validator,
          builder: (FormFieldState<String> field) {
            final state = field as CtFormInputState;
            return TextField(
              controller: state._controller,
              onChanged: state.didChange,
              decoration: InputDecoration(
                errorText: field.errorText,
              ),
            );
          },
        );

  @override
  CtFormInputState createState() => CtFormInputState();
}

class CtFormInputState extends FormFieldState<String> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
