part of widgets;

/// 文本样式枚举，对应Material 3设计规范
enum OxTextStyle {
  /// 显示文本 - 最大的文本样式，用于短而重要的文本或数字
  displayLarge,
  displayMedium,
  displaySmall,

  /// 标题文本 - 用于页面标题和重要的标题
  headlineLarge,
  headlineMedium,
  headlineSmall,

  /// 标题文本 - 用于较小的标题
  titleLarge,
  titleMedium,
  titleSmall,

  /// 正文文本 - 用于长篇内容
  bodyLarge,
  bodyMedium,
  bodySmall,

  /// 标签文本 - 用于按钮、标签等
  labelLarge,
  labelMedium,
  labelSmall,
}

/// 重构后的文本组件，使用主题系统
class OxText extends StatelessWidget {
  /// 要显示的文本内容
  final String? text;

  /// 文本样式类型，基于Material 3设计规范
  final OxTextStyle? style;

  /// 自定义字体大小（可选，会覆盖样式中的字体大小）
  final double? fontSize;

  /// 自定义颜色（可选，会覆盖样式中的颜色）
  final Color? color;

  /// 最大行数
  final int? maxLines;

  /// 超出部分处理方案
  final TextOverflow? overflow;

  /// 自定义字体粗细（可选，会覆盖样式中的字体粗细）
  final FontWeight? fontWeight;

  /// 文本对齐方式
  final TextAlign? textAlign;

  /// 文本装饰（下划线、删除线等）
  final TextDecoration? decoration;

  /// 字母间距
  final double? letterSpacing;

  /// 行高
  final double? height;

  const OxText(
    this.text, {
    Key? key,
    this.style,
    this.fontSize,
    this.color,
    this.maxLines,
    this.overflow,
    this.fontWeight,
    this.textAlign,
    this.decoration,
    this.letterSpacing,
    this.height,
  }) : super(key: key);

  /// 根据样式类型获取对应的TextStyle
  TextStyle? _getTextStyleFromTheme(
      BuildContext context, OxTextStyle styleType) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    switch (styleType) {
      case OxTextStyle.displayLarge:
        return textTheme.displayLarge;
      case OxTextStyle.displayMedium:
        return textTheme.displayMedium;
      case OxTextStyle.displaySmall:
        return textTheme.displaySmall;
      case OxTextStyle.headlineLarge:
        return textTheme.headlineLarge;
      case OxTextStyle.headlineMedium:
        return textTheme.headlineMedium;
      case OxTextStyle.headlineSmall:
        return textTheme.headlineSmall;
      case OxTextStyle.titleLarge:
        return textTheme.titleLarge;
      case OxTextStyle.titleMedium:
        return textTheme.titleMedium;
      case OxTextStyle.titleSmall:
        return textTheme.titleSmall;
      case OxTextStyle.bodyLarge:
        return textTheme.bodyLarge;
      case OxTextStyle.bodyMedium:
        return textTheme.bodyMedium;
      case OxTextStyle.bodySmall:
        return textTheme.bodySmall;
      case OxTextStyle.labelLarge:
        return textTheme.labelLarge;
      case OxTextStyle.labelMedium:
        return textTheme.labelMedium;
      case OxTextStyle.labelSmall:
        return textTheme.labelSmall;
    }
  }

  @override
  Widget build(BuildContext context) {
    final String displayText = text ?? '';
    final theme = Theme.of(context);

    // 获取基础样式
    TextStyle? baseStyle;
    if (style != null) {
      baseStyle = _getTextStyleFromTheme(context, style!);
    } else {
      // 默认使用bodyMedium样式
      baseStyle = theme.textTheme.bodyMedium;
    }

    // 应用自定义属性，覆盖基础样式
    final finalStyle = baseStyle?.copyWith(
      fontSize: fontSize,
      color: color,
      fontWeight: fontWeight,
      decoration: decoration,
      letterSpacing: letterSpacing,
      height: height,
    );

    return Text(
      displayText,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      style: finalStyle,
    );
  }
}
