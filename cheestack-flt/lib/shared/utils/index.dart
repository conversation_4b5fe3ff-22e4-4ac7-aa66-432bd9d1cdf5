library utils;

import 'dart:async';
import 'dart:convert' hide Codec;
import 'dart:io';
import 'dart:ui';
import 'package:audio_session/audio_session.dart';
import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:crypto/crypto.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos.dart';
import 'package:tencentcloud_cos_sdk_plugin/cos_transfer_manger.dart';
import 'package:tencentcloud_cos_sdk_plugin/enums.dart';
import 'package:tencentcloud_cos_sdk_plugin/fetch_credentials.dart';
import 'package:tencentcloud_cos_sdk_plugin/pigeon.dart';
import 'package:tencentcloud_cos_sdk_plugin/transfer_task.dart';
import 'package:uuid/uuid.dart';

part 'access.dart';
part 'assets_picker.dart';
part 'common.dart';
part 'console.dart';
part 'convert.dart';
part 'date.dart';
part 'debounce.dart';
part 'input_formatter.dart';
part 'list.dart';
part 'screen.dart';
part 'string_utils.dart';
part '../../common/data/strings.dart';
part 'tts.dart';
part 'image.dart';
part 'validator.dart';
part 'sound.dart';
part 'timer.dart';
part 'keyboard.dart';
part 'cos.dart';
part 'image_picker.dart';
part 'card_asset.dart';
part 'calculate.dart';
part 'devices.dart';

// part '../controllers/justaudio.dart';
// part 'cos.dart';
