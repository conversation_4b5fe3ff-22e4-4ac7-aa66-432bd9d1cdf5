part of chv_audio;

class SaudioPlayListView extends StatefulWidget {
  const SaudioPlayListView({Key? key}) : super(key: key);

  @override
  State<SaudioPlayListView> createState() => _SaudioPlayListViewState();
}

class _SaudioPlayListViewState extends State<SaudioPlayListView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<ChvAudioController>(
      init: ChvAudioController(),
      builder: (controller) {
        return SmartRefresher(
          controller: controller.refreshController,
          onRefresh: controller._loadPlaylist,
          enablePullUp: true,
          enablePullDown: true,
          child: _buildView(),
        );
      },
    );
  }

  /// 主视图
  Widget _buildView() {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return Column(
            children: [
              if (controller._playlist.isNotEmpty)
                OxText(
                    "${controller.currentMediaItem?.id}/${controller._playlist.length}"),
              const SizedBox(height: 16),
              const Expanded(child: SongAlbumCover()),
              const SizedBox(height: 16),

              const Center(child: CurrentSongTitle()),
              // const AudioProgressBar().sTop(),
              const AudioControlButtons(),
            ],
          );
        });
  }

  @override
  bool get wantKeepAlive => true;
}

class SongAlbumCover extends StatelessWidget {
  const SongAlbumCover({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return Simage(
              url: controller.currentMediaItem?.album ?? "",
              height: MediaQuery.of(context).size.width * 0.8);
        });
  }
}

class CurrentSongTitle extends StatelessWidget {
  const CurrentSongTitle({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return Text(
            controller.currentMediaItem?.displaySubtitle ??
                LocaleKeys.listeningNoAudio.tr,
            style: Theme.of(context).textTheme.titleLarge,
          );
        });
  }
}

class Playlist extends StatelessWidget {
  const Playlist({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return ListView.builder(
            itemCount: controller._playlist.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(controller._playlist[index]),
              );
            },
          );
        });
  }
}

// class AddRemoveSongButtons extends StatelessWidget {
//   const AddRemoveSongButtons({Key? key}) : super(key: key);
//   @override
//   Widget build(BuildContext context) {
//     // final controller = getIt<PageManager>();
//     return GetBuilder<ChvAudioController>(
//         init: ChvAudioController(),
//         builder: (controller) {
//           return Padding(
//             padding: const EdgeInsets.only(bottom: 20.0),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 FloatingActionButton(
//                   onPressed: controller.add,
//                   child: const Icon(Icons.add),
//                 ),
//                 FloatingActionButton(
//                   onPressed: controller.remove,
//                   child: const Icon(Icons.remove),
//                 ),
//               ],
//             ),
//           );
//         });
//   }
// }

class AudioProgressBar extends StatelessWidget {
  const AudioProgressBar({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return ProgressBar(
            progress: controller.progressBarState.current,
            buffered: controller.progressBarState.buffered,
            total: controller.progressBarState.total,
            onSeek: controller.seek,
          );
        });
  }
}

class AudioControlButtons extends StatelessWidget {
  const AudioControlButtons({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: IconSize.titleXXL + AppTheme.spacingMedium,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // RepeatButton(),
          PreviousSongButton(),
          PlayButton(),
          NextSongButton(),
          // ShuffleButton(),
        ],
      ),
    );
  }
}

class RepeatButton extends StatelessWidget {
  const RepeatButton({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          Icon icon;
          switch (controller.repeatButtonNotifier) {
            case RepeatState.off:
              icon = Icon(Icons.repeat,
                  color: Theme.of(context).colorScheme.outline);
              break;
            case RepeatState.repeatSong:
              icon = const Icon(Icons.repeat_one);
              break;
            case RepeatState.repeatPlaylist:
              icon = const Icon(Icons.repeat);
              break;
          }
          return IconButton(
            icon: icon,
            onPressed: controller.repeat,
          );
        });
  }
}

class PreviousSongButton extends StatelessWidget {
  const PreviousSongButton({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return IconButton(
            icon: const Icon(Icons.skip_previous),
            onPressed: (controller.isFirstSong) ? null : controller.previous,
          );
        });
  }
}

class PlayButton extends StatelessWidget {
  const PlayButton({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          double iconSize = IconSize.titleXXL;
          switch (controller.playState) {
            case PlayState.loading:
              return Container(
                margin: const EdgeInsets.all(8.0),
                width: iconSize,
                height: iconSize,
                child: const CircularProgressIndicator(),
              );
            case PlayState.paused:
              return IconButton(
                icon: const Icon(Icons.play_arrow),
                iconSize: iconSize,
                onPressed:
                    controller._playlist.isEmpty ? null : controller.play,
              );
            case PlayState.playing:
              return IconButton(
                icon: const Icon(Icons.pause),
                iconSize: iconSize,
                onPressed: controller.pause,
              );
          }
        });
  }
}

class NextSongButton extends StatelessWidget {
  const NextSongButton({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return IconButton(
            icon: const Icon(Icons.skip_next),
            onPressed: (controller.isLastSong) ? null : controller.next,
          );
        });
  }
}

class ShuffleButton extends StatelessWidget {
  const ShuffleButton({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChvAudioController>(
        init: ChvAudioController(),
        builder: (controller) {
          return IconButton(
            icon: (controller.isShuffleModeEnabledNotifier)
                ? const Icon(Icons.shuffle)
                : Icon(Icons.shuffle,
                    color: Theme.of(context).colorScheme.outline),
            onPressed: controller.shuffle,
          );
        });
  }
}
