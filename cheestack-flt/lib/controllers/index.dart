library controllers;

import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/controllers/load_state_controller.dart';
import 'package:cheestack_flt/controllers/speech_recognition_state.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/refresh_controller_factory.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:ionicons/ionicons.dart';
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:just_audio/just_audio.dart' as just_audio;
import 'package:sherpa_onnx/sherpa_onnx.dart' as sherpa_onnx;
import 'package:path/path.dart' as p;
import 'package:record/record.dart';
import 'package:string_similarity/string_similarity.dart';
import 'package:lpinyin/lpinyin.dart';

import 'dart:io';
import 'package:path_provider/path_provider.dart';

// 导出控制器

// part 'audio_controller.dart';
part 'config.dart';
part 'refresh.dart';
part 'justaudio_controller.dart';
part 'sherpa_onnx_controller.dart';
