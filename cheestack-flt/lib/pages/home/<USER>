part of home_page;

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: _buildBookList(controller),
          // body: Container(),
          // body: _buildRefreshContent(controller),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;

  _buildAppBar() {
    return OxAppBar(
      titleSpacing: 0,
      title: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppTheme.margin),
        child: GetBuilder<HomeController>(
            init: HomeController(),
            builder: (controller) {
              return SinputSearch(
                readOnly: true,
                hintText: LocaleKeys.formSearchBooks.tr,
                onTap: controller.gotoBookSearch,
              );
            }),
      ),
    );
  }

  Widget _itemBuilder(BuildContext context, int index) {
    return GetBuilder<HomeController>(
        init: HomeController(),
        builder: (controller) {
          List<BookModel> items = controller.refreshDataList;
          if (items.isEmpty) return const SizedBox();
          
          final theme = Theme.of(context);
          final colorScheme = theme.colorScheme;
          
          return InkWell(
            onTap: () => controller.toBookInfo(items[index]),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.w),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16.w),
                child: Container(
                  color: colorScheme.surface,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 图片部分
                      AspectRatio(
                        aspectRatio: 1,
                        child: Simage(
                          url: items[index].cover ?? "",
                          fit: BoxFit.cover,
                          width: double.infinity,
                        ),
                      ),

                      // 文字部分 - 使用半透明叠加效果
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 标题
                            Text(
                              items[index].name ?? "",
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.primary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 6.h),
                            // 简介
                            Text(
                              items[index].brief ?? "",
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface
                                    .withValues(alpha: 0.7),
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  _buildBookList(HomeController controller) {
    return SmartRefresher(
      controller: controller.refreshController,
      onRefresh: controller.onRefresh,
      onLoading: controller.onLoading,
      enablePullUp: true,
      enablePullDown: true,
      header: const WaterDropHeader(
        waterDropColor: AppTheme.primary,
        complete: SizedBox.shrink(),
        refresh: SizedBox.shrink(),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: MasonryGridView.count(
          crossAxisCount: DeviceUtil.isTablet() ? 4 : 2,
          mainAxisSpacing: 20.h, // 增加间距使设计更加通透
          crossAxisSpacing: 16.w,
          itemCount: controller.refreshDataList.length,
          itemBuilder: (context, index) {
            if ((index + 1) >= controller.refreshDataList.length) {
              return const SizedBox.shrink();
            } else {
              return _itemBuilder(context, index);
            }
          },
        ),
      ),
    );
  }
}
