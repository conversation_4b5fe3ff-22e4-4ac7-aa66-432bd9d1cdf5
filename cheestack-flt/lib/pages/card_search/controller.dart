import 'dart:convert';

import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CardSearchController extends PullToRefreshController<CardModel> {
  CardSearchController();

  /// 搜索框控制器
  TextEditingController textEditingController = TextEditingController();

  /// 搜索框输入值变化回调
  Function(String)? onInputValueChanged;

  /// 定义`filters`对象
  String keyword = "";
  BookModel? bookModel;

  Future _initData() async {
    await fetchRefreshDataList();
  }

  void onTap() {}

  @override
  void onInit() {
    super.onInit();
    // get the bookId from the arguments
    final args = Get.arguments;
    if (args != null && args is BookModel) bookModel = args;
  }

  @override
  void onReady() async {
    super.onReady();
    // 防抖函数
    onInputValueChanged = debounceFnWithArgs((val) async {
      // Console.log("onInputValueChanged");
      // Console.log(val);
      if (val.isNotNullOrEmpty) {
        keyword = val;
        // Console.log(keyword);
      }
      await onRefresh();
    });

    await _initData();
  }

  @override
  Future fetchRefreshDataList({bool refresh = false}) async {
    /// define filters json
    Map<String, dynamic> filters = {
      "created_at__lte": refreshConfigModel.utcString,
      "title__icontains": keyword,
      if (bookModel != null) "books": bookModel?.id
    };

    /// 请求数据
    await asyncRequest(
      () => OxHttp.to.get(HttpUrl.cards, queryParameters: {
        "skip": refreshConfigModel.skip,
        "limit": refreshConfigModel.limit,
        "filters": jsonEncode(filters),
      }),
      onSuccess: (data) {
        List<CardModel> refreshModelList =
            data.map<CardModel>((x) => CardModel.fromJson(x)).toList();
        // 判断是否已经加载完毕
        refreshConfigModel.finish =
            refreshModelList.length < (refreshConfigModel.limit);
        // 添加数据到刷新列表
        refreshDataList.addAll(refreshModelList);
        loadSuccess();
        update();
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
  }

  // void toCardInfoPage(int? id) async {
  //   if (id == null) return;
  //   bool result = await Get.toNamed(AppRoutes.review, arguments: {
  //     "type": StudyType.info,
  //     "id": id,
  //   });
  //   // 如果返回值是`true`，则刷新页面
  //   if (result == true) {
  //     await onRefresh();
  //   }
  // }
  /// 函数用于跳转页面到卡片详情页
  /// @param id int: 卡片id
  /// 接收页面返回值, 判断返回值是否为类型为`true`
  /// 如果返回值为`true`则刷新页面数据
  Future goToCardInfo(int index) async {
    CardModel cardModel = refreshDataList[index];

    // 获取返回数据
    final result = await Get.toNamed(AppRoutes.review, arguments: {
      "type": StudyType.info,
      "cardModel": cardModel,
    });
    Console.log(result);
    // 如果返回值是`true`，则刷新页面
    if (result == true) {
      await onRefresh();
    }
  }
}
