import 'package:cheestack_flt/common/data/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ionicons/ionicons.dart';

import 'index.dart';

class CardSearchPage extends StatefulWidget {
  const CardSearchPage({Key? key}) : super(key: key);
  @override
  State<CardSearchPage> createState() => _CardSearchPageState();
}

class _CardSearchPageState extends State<CardSearchPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return const _CardSearchViewGetX();
  }
}

class _CardSearchViewGetX extends GetView<CardSearchController> {
  const _CardSearchViewGetX({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CardSearchController>(
      init: CardSearchController(),
      builder: (_) {
        return Scaffold(
          appBar: buildAppBar(),
          body: SafeArea(
            child: _buildView(),
          ),
        );
      },
    );
  }

  PreferredSizeWidget? buildAppBar() {
    return OxAppBar(
      title: SinputSearch(
        autofocus: false,
        hintText: LocaleKeys.formSearchCards.tr,
        controller: controller.textEditingController,
        onChanged: controller.onInputValueChanged,
      ),
    );
  }

  // 主视图
  Widget _buildView() {
    return SloadStateWrapper(
        state: controller.loadState,
        child: SscrollView(
          builder: cardItemBuilder(),
          refreshController: controller.refreshController,
          childCount: controller.refreshDataList.length,
          onLoading: controller.onLoading,
          onRefresh: controller.onRefresh,
        ));
  }

  Widget? Function(BuildContext, int) cardItemBuilder() {
    return (context, index) {
      if (controller.refreshDataList.isEmpty) {
        return const SizedBox();
      }
      return CustomCard.common(
          image: Simage(
            url: controller.refreshDataList[index].cardAssets
                ?.firstWhereNullable((e) {
              return e.type == CardAssetType.primaryImage.value;
            })?.url,
          ),
          title: Text(
            controller.refreshDataList[index].title ?? "卡片概述",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(
            controller.refreshDataList[index].question ?? "问题描述",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          label: Row(children: [
            Icon(
              Ionicons.at,
              size: IconSize.caption,
            ),
            Expanded(
                child: Text(
              controller.refreshDataList[index].user?.username ?? "问题描述",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ))
          ]),
          onTap: () => controller.goToCardInfo(index));
    };
  }
}
