library feedback;

import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:cheestack_flt/shared/extensions/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

part 'controller.dart';
part 'view.dart';
