part of bookshelf;

class BookshelfPage extends StatefulWidget {
  const BookshelfPage({Key? key}) : super(key: key);

  @override
  State<BookshelfPage> createState() => _BookshelfPageState();
}

class _BookshelfPageState extends State<BookshelfPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(title: const Text("bookshelf")),
      body: SafeArea(
        child: _buildView(),
      ),
    );
  }

  /// 主视图
  Widget _buildView() {
    return const Column(
      children: [
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // _buildCategoryVertical(),
              // Expanded(child: BookListPage(tag: 'category')),
            ],
          ),
        ),
      ],
    );
  }

  // Widget _buildCategoryHorizontal() {
  //   return SingleChildScrollView(
  //     scrollDirection: Axis.horizontal,
  //     child: Container(
  //       height: FontSize.regular * 2.5,
  //       child: GetBuilder<HomeController>(builder: (_) {
  //         // List<CategoryModel> categories =
  //         //     HomeController.categoryLevelOneAll;
  //         return ToggleButtons(
  //             isSelected: categories.map((e) => e.selected).toList(),
  //             color: Theme.of(Get.context!).colorScheme.onPrimary,
  //             renderBorder: false,
  //             // fillColor: Theme.of(Get.context!).colorScheme.inversePrimary,
  //             selectedColor: Theme.of(Get.context!).colorScheme.inversePrimary,
  //             onPressed: (index) {
  //               controller.onCategoryOneTap(categories[index]);
  //             },
  //             children: categories
  //                 .map((e) => Container(
  //                     width: 30.w, child: Center(child: Text(e.name ?? ""))))
  //                 .toList());
  //       }),
  //     ),
  //   );
  // }

  // Widget _buildSearchIcon() {
  //   return Container(
  //     margin: margin4,
  //     child: mIconButton(
  //       icon: Icon(Icons.search),
  //       onPressed: controller.toBookListCommon,
  //     ),
  //   );
  // }

  // Widget _buildCategoryVertical() {
  //   return SingleChildScrollView(
  //     child: GetBuilder<HomeController>(builder: (_) {
  //       List<CategoryModel> categories = HomeController.to.categoryLevelTwoAll;
  //       return Container(
  //           // margin: EdgeInsets.only(left: 4.w),
  //           child: ToggleButtons(
  //               direction: Axis.vertical,
  //               isSelected: categories.map((e) => e.selected).toList(),
  //               selectedColor: Theme.of(Get.context!).colorScheme.primary,
  //               onPressed: (index) {
  //                 controller.onCategoryTwoTab(categories[index]);
  //               },
  //               children: categories
  //                   .map(
  //                     (e) => Container(
  //                       margin: margin6,
  //                       child: Center(
  //                         child: Text(e.name ?? ""),
  //                       ),
  //                     ),
  //                   )
  //                   .toList()));
  //     }),
  //   );
  // }
}
