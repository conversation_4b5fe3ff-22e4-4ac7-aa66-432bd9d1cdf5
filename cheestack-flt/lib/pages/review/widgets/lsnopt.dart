part of review;

class ListeningOptionView extends GetView<ReviewController> {
  const ListeningOptionView({super.key});

  @override
  Widget build(BuildContext context) {
    // 等待插件加载完成后播放音乐
    WidgetsBinding.instance.addPostFrameCallback((_) {
      OxAudioController.to
          .play(controller.getAssetUrl(CardAssetType.primaryAudio));
    });
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (_) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(child: _buildOptionGrid(controller)),
              const SizedBox(height: 8),
              _buildAudioBar(),
              const SizedBox(height: 16),
              Sbutton(
                  width: double.infinity,
                  shape: SbuttonShape.outline,
                  size: SbuttonSize.large,
                  onPressed: controller.onOptionSubmit,
                  child: const OxText("提交")),
            ],
          );
        });
  }

  Row _buildAudioBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Sbutton.icon(
            icon: Icon(
              Icons.volume_up_rounded,
              size: IconSize.titleXL,
            ),
            onPressed: () {
              OxAudioController.to.play(ChvCardAsset.getAssetUrl(
                  controller.curCard.cardAssets, CardAssetType.primaryAudio));
            }),
        const SizedBox(width: 18),
        Sbutton.icon(
            icon: Icon(
              Icons.hourglass_top_outlined,
              size: IconSize.titleXL,
            ),
            onPressed: () {
              OxAudioController.to.play(
                  ChvCardAsset.getAssetUrl(controller.curCard.cardAssets,
                      CardAssetType.primaryAudio),
                  speed: 0.6);
            })
      ],
    );
  }

  GridView _buildOptionGrid(ReviewController controller) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: controller.options.length >= 2 ? 2 : 1,
        mainAxisSpacing: 4,
        crossAxisSpacing: 4,
        childAspectRatio: 7 / 8,
      ),
      itemCount: controller.options.length,
      itemBuilder: (BuildContext context, int index) {
        OptionModel option = controller.options[index];
        return InkWell(
          onTap: () {
            int qty = controller.options
                .where((item) => item.isCorrect == true)
                .length;
            Console.log(qty);
            if (qty == 1) {
              for (var item in controller.options) {
                item.selected = false;
              }
            }
            option.selected = !option.selected;
            controller.update();
          },
          child: Card(
            // margin: const EdgeInsets.all(AppTheme.margin),

            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                  color: option.selected ? Colors.green : Colors.transparent,
                  width: 3.0),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                controller.options[index].imageUrl.isNotNullOrEmpty
                    ? Expanded(
                        child: Simage(
                        url: controller.options[index].imageUrl ?? "",
                        fit: BoxFit.contain,
                      ))
                    : const SizedBox(),
                if (controller.options[index].text != null)
                  OxText(controller.options[index].text)
                      .marginAll(AppTheme.marginS)
              ],
            ),
          ),
        );
      },
    );
  }
}
