// ignore_for_file: non_constant_identifier_names

part of review;

class JmtV1 extends StatefulWidget {
  final Function()? onForgot;
  final Function()? onRemembered;

  const JmtV1({
    this.onForgot,
    this.onRemembered,
    super.key,
  });

  @override
  State<JmtV1> createState() => _JmtV1State();
}

class _JmtV1State extends State<JmtV1> {
  bool timeUp = false;

  @override
  void initState() {
    super.initState();

    Future.delayed(
        const Duration(seconds: 1),
        () => setState(() {
              timeUp = true;
            }));
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Sbutton(
            backgroundColor: Theme.of(Get.context!).colorScheme.error,
            onPressed: timeUp == true ? widget.onForgot : null,
            shape: SbuttonShape.outline,
            size: SbuttonSize.large,
            child: const OxText("模糊"),
          ),
        ),
        Expanded(
          child: Sbutton(
            onPressed: timeUp == true ? widget.onRemembered : null,
            shape: SbuttonShape.outline,
            size: SbuttonSize.large,
            child: const OxText("确定"),
          ),
        ),
      ],
    );
  }
}
