part of review;

class ChineseReadingView extends StatefulWidget {
  const ChineseReadingView({
    Key? key,
  }) : super(key: key);

  @override
  ChineseReadingViewState createState() => ChineseReadingViewState();
}

class ChineseReadingViewState extends State<ChineseReadingView> {
  late ReviewController controller;
  double? similarityScore;
  double? maxSimilarityScore;
  bool isRecording = false;
  bool isScoring = false;

  /// the final result string
  String resultString = "";

  /// the limit time to speech, according to the audio duration
  Timer timer = Timer(Duration.zero, () {});

  /// try times
  int tryTimes = 0;
  int maxTryTimes = 5;
  int effectiveTry = 0;
  int maxEffectiveTry = 3;
  bool isAllowClick = true;
  bool showHintImage = false;

  /// 当前时间
  DateTime recordTime = DateTime.now();

  _stopPlayOrRecord() async {
    await Ssound.to.startRecord();
    await Ssound.to.stopPlay();
    await OxAudioController.to.stop();
  }

  @override
  void initState() {
    super.initState();

    /// init controller
    if (!Get.isRegistered<ReviewController>()) {
      Get.lazyPut<ReviewController>(() => ReviewController());
    }
    controller = Get.find<ReviewController>();

    /// 确保组件创建完后再播放音乐
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   SaudioController.to.play(
    //       controller.getAssetUrl(CardAssetType.secondaryAudio),
    //       isForceListening: true);
    // });
  }

  Future _startRecording() async {
    setState(() {
      isAllowClick = false;
      isRecording = true;
    });
    recordTime = DateTime.now();
    await _stopPlayOrRecord();
    await Ssound().startRecord();
    timer = Timer(_getRecorderTimeOutTime(), () async {
      await _stopRecording();
    });
  }

  Future _stopRecording() async {
    if (DateTime.now().difference(recordTime).inMilliseconds < 500) {
      ShowToast.fail('录音时间太短');
      setState(() {
        isRecording = false;
      });
      return;
    }
    await Ssound.to.stopRecord();

    /// 更新界面状态
    setState(() {
      isRecording = Ssound.to.isRecording;
      isScoring = true;
    });

    /// 发送录音
    try {
      // 发送音频并获取结果
      double result = await controller.getWhisperResult() ?? "";
      setState(() {
        isScoring = false;
        similarityScore = result;

        /// update maxSimilarityScore
        maxSimilarityScore = max(maxSimilarityScore ?? 0, similarityScore ?? 0);
        Console.log("similarityScore:$similarityScore");
        tryTimes++;
        if ((similarityScore ?? 0) > 0.2) {
          effectiveTry++;
        }

        if (checkResult()) {
          /// 异步播放一次原音
          OxAudioController.to.play(
            controller.getAssetUrl(CardAssetType.primaryAudio),
            isForceListening: true,
          );
        }

        /// 重试次数+1
      });
    } catch (e) {
      Console.log(e);
      setState(() {
        isScoring = false;
      });
    }
  }

  _getRecorderTimeOutTime() {
    Duration duration = OxAudioController.to.duration;
    if (duration == Duration.zero) {
      Console.log(duration);
      return const Duration(seconds: 5);
    } else {
      return duration + const Duration(seconds: 2);
    }
  }

  /// 重置当前状态
  _resetStatus() {
    setState(() {
      maxSimilarityScore = null;
      similarityScore = null;
      resultString = "";
      tryTimes = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return GetBuilder<OxAudioController>(
              init: OxAudioController(),
              builder: (_) {
                return Column(
                  children: [
                    Expanded(
                        child: Container(
                      margin: EdgeInsets.all(AppTheme.margin),
                      child: Column(
                        children: [
                          Expanded(child: _buidCardInfo()),
                          SizedBox(height: 8.w),
                          _buildWaveOrRateBar(),
                          SizedBox(height: 8.w),
                        ],
                      ),
                    )),
                    _buildActionBar(),
                  ],
                );
              });
        });
  }

  _buidCardInfo() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // 如果有图片,显示图片
              if (controller.getAssetUrl(CardAssetType.primaryImage) != null)
                Expanded(
                    child: Simage(
                  url: controller.getAssetUrl(CardAssetType.primaryImage) ?? "",
                  fit: BoxFit.contain,
                )),
              const SizedBox(height: 16),
              _buildText(),
              const SizedBox(height: 8),
            ],
          );
        });
  }
  // _buildImage() {
  //   if (showHintImage) {
  //     return Simage(
  //       url: controller.getAssetUrl(CardAssetType.primaryImage) ?? "",
  //       fit: BoxFit.contain,
  //     );
  //   } else {}
  // }

  // _buildPinyin() {
  //   return Stext(
  //     PinyinHelper.getPinyinE(
  //       controller.curCard.question ?? "",
  //       format: PinyinFormat.WITH_TONE_MARK,
  //     ),
  //     fontSize: IconSize.body,
  //   );
  // }

  Widget _buildText() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // _buildPinyin(),
              // const SizedBox(width: 8),
              EasyRichText("${controller.curCard.question}",
                  defaultStyle: TextStyle(fontSize: FontSize.titleXXL),
                  textAlign: TextAlign.center,
                  patternList: [
                    ...controller.unmatch.map((e) {
                      return EasyRichTextPattern(
                        targetString: e,
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: FontSize.titleXL,
                        ),
                      );
                    }).toList(),
                    EasyRichTextPattern(
                      targetString: controller.curCard.title,
                      style: TextStyle(
                        color: AppTheme.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSize.titleXXXL,
                      ),
                    ),
                  ]),
            ],
          );
        });
  }

  _buildWaveOrRateBar() {
    Widget view;
    if (isScoring) {
      view = SpinKitCircle(
        color: AppTheme.primary,
        size: IconSize.titleXL,
      );
    } else if (isRecording) {
      view = ChvAudioWave();
    } else if (similarityScore != null) {
      view = ChvRate(
        initialValue: (similarityScore ?? 0) * 5,
        readOnly: true,
        allowHalf: true,
      );
    } else {
      view = const SizedBox.shrink();
    }
    return SizedBox(height: 60, child: view);
  }

  Widget _buildForgotButton({required Icon icon, double speed = 1.0}) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Sbutton(
              size: SbuttonSize.large,
              shape: SbuttonShape.outline,
              backgroundColor: AppTheme.warning,
              onPressed: () {
                if (isRecording) return;
                controller.isCorrect = false;
                controller.update();
                OxAudioController.to.play(
                    ChvCardAsset.getAssetUrl(controller.curCard.cardAssets,
                        CardAssetType.primaryAudio),
                    isForceListening: true,
                    speed: speed);
              },
              child: const OxText('忘记'));
        });
  }

  Widget _buildActionBar() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Row(
            children: [
              if (!checkResult())
                Expanded(
                  child: _buildForgotButton(
                      icon: Icon(
                    Icons.volume_up,
                    size: IconSize.titleL,
                    color: AppTheme.primary,
                  )),
                ),
              Expanded(child: _buildRecordBar()),
              if (checkResult()) Expanded(child: _buildResultBar()),
            ],
          );
        });
  }

  _buildRecordBar() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          if (isScoring) {
            return const Sbutton(
                size: SbuttonSize.large,
                shape: SbuttonShape.outline,
                backgroundColor: AppTheme.primary,
                onPressed: null,
                child: OxText("分析中..."));
          } else if (isRecording) {
            return Sbutton(
                size: SbuttonSize.large,
                shape: SbuttonShape.outline,
                backgroundColor: AppTheme.primary,
                child: const OxText("停止录音"),
                onPressed: () async {
                  if (timer.isActive) timer.cancel();
                  await _stopRecording();
                });
          } else {
            return Sbutton(
              size: SbuttonSize.large,
              shape: SbuttonShape.outline,
              backgroundColor: AppTheme.primary,
              onPressed: _startRecording,
              child: const OxText("点击录音"),
            );
          }
        });
  }

  Widget _buildResultBar() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Sbutton(
            onPressed: checkResult()
                ? () {
                    controller.toPageIndex();
                    // controller.onResultButtonClick(true);
                    _resetStatus();
                  }
                : null,
            size: SbuttonSize.large,
            shape: SbuttonShape.outline,
            backgroundColor: AppTheme.success,
            child: const OxText("下一步"),
          );
        });
  }

  bool checkResult() {
    return (((maxSimilarityScore ?? 0) > 0.6 || tryTimes >= maxTryTimes) ||
        effectiveTry >= 3);
  }
}
