part of review;

class HanziWriter extends StatefulWidget {
  final String word;
  final Function({bool isCorrect, int plus}) onNext;

  const HanziWriter({required this.word, super.key, required this.onNext});

  @override
  HanziWriterState createState() => HanziWriterState();
}

class HanziWriterState extends State<HanziWriter>
    with TickerProviderStateMixin {
  bool readyToPass = false;
  StrokeOrderAnimationController? _completedController;
  late Future<StrokeOrderAnimationController> _animationController;
  late ReviewController controller;

  @override
  void initState() {
    super.initState();

    /// 获取controller
    if (!Get.isRegistered<ReviewController>()) {
      Get.lazyPut<ReviewController>(() => ReviewController());
    }
    controller = Get.find<ReviewController>();

    /// 定义动画控制器
    _animationController = _loadStrokeOrder(widget.word);
    _animationController.then((a) => _completedController = a);
  }

  @override
  void dispose() {
    _completedController?.dispose();
    super.dispose();
  }

  Future<StrokeOrderAnimationController> _loadStrokeOrder(
    String character,
  ) async {
    // 读取 assets 中的 JSON 文件为字符串
    String jsonString =
        await rootBundle.loadString("assets/hanzi/$character.json");

    final strokeController = StrokeOrderAnimationController(
      showOutline: true,
      outlineWidth: 1,
      showBackground: true,
      brushWidth: 16,
      hintAnimationSpeed: 2,
      strokeAnimationSpeed: 2,
      hintAfterStrokes: 2,
      strokeColor: AppTheme.primary,
      backgroundColor: Colors.grey.shade300,
      StrokeOrder(jsonString),
      this,
      onQuizCompleteCallback: (QuizSummary summary) {
        OxAudioController.to.play("assets/audios/powerup-success.wav");
        setState(() {
          readyToPass = true;
        });
      },
    );
    return strokeController;
    // 解析 JSON 字符串为 Map
  }

  @override
  Widget build(BuildContext context) {
    return _buildView();
  }

  Widget _buildView() {
    return FutureBuilder<StrokeOrderAnimationController>(
      future: _animationController,
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return const CircularProgressIndicator();
        }
        if (snapshot.hasData) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const OxText("请按笔画顺序写出汉字"),
              _buildStrokeOrderAnimation(snapshot.data!),
              _buildAnimationControls(snapshot.data!),
            ],
          );
        }
        if (snapshot.hasError) {
          return const Text("不支持的文本,请修改");
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildStrokeOrderAnimation(StrokeOrderAnimationController controller) {
    return StrokeOrderAnimator(
      controller,
      size: const Size(350, 350),
      key: UniqueKey(),
    );
  }

  // start the quiz
  startQuiz() {
    if (_completedController?.isAnimating ?? false) {
      _completedController?.stopAnimation();
    }
    _completedController?.startQuiz();
  }

  startAnimation() {
    if (_completedController?.isQuizzing ?? false) {
      _completedController?.stopQuiz();
    }
    _completedController?.startAnimation();
  }

  showFullStrokes() {
    if (_completedController?.isQuizzing ?? false) {
      _completedController?.stopQuiz();
    }
    _completedController?.showFullCharacter();
  }

  showNextStroke() {
    if (_completedController?.isQuizzing ?? false) {
      _completedController?.stopQuiz();
    }
    _completedController?.nextStroke();
    _completedController?.startQuiz();
  }

  Widget _buildAnimationControls(
      StrokeOrderAnimationController strokeController) {
    // controller.startAnimation();
    strokeController.startQuiz();

    return ListenableBuilder(
      listenable: strokeController,
      builder: (context, child) {
        return readyToPass
            ? Sbutton(
                width: double.infinity,
                shape: SbuttonShape.outline,
                size: SbuttonSize.large,
                backgroundColor: AppTheme.success,
                onPressed: () => widget.onNext(isCorrect: controller.isCorrect),
                child: const Text('下一题'),
              )
            : Row(
                children: <Widget>[
                  Expanded(
                      child: Sbutton(
                    shape: SbuttonShape.outline,
                    backgroundColor: AppTheme.primary,
                    size: SbuttonSize.large,
                    onPressed: startAnimation,
                    child: const Text('播放动画'),
                  )),
                  Expanded(
                      child: Sbutton(
                    shape: SbuttonShape.outline,
                    size: SbuttonSize.large,
                    backgroundColor: AppTheme.success,
                    onPressed: startQuiz,
                    child: const Text('开始测验'),
                  )),
                ],
              );
      },
    );
  }
}
