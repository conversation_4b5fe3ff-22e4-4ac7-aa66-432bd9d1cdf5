part of review;

class Say<PERSON>iew extends StatefulWidget {
  const SayView({
    Key? key,
  }) : super(key: key);

  @override
  SayViewState createState() => SayViewState();
}

class SayViewState extends State<SayView> {
  late ReviewController controller;
  double? similarityScore;
  double? maxSimilarityScore;

  bool isRecording = false;
  bool isScoring = false;

  /// the final result string
  String resultString = "";

  /// the limit time to speech, according to the audio duration
  Timer timer = Timer(Duration.zero, () {});

  /// try times
  int tryTimes = 0;
  int maxTryTimes = 5;
  int effectiveTry = 0;
  int maxEffectiveTry = 3;
  bool isAllowClick = true;

  /// 当前时间
  DateTime recordTime = DateTime.now();

  _stopPlayOrRecord() async {
    await Ssound.to.startRecord();
    await Ssound.to.stopPlay();
    await OxAudioController.to.stop();
  }

  @override
  void initState() {
    super.initState();

    if (!Get.isRegistered<ReviewController>()) {
      Get.lazyPut<ReviewController>(() => ReviewController());
    }

    /// init JustAudioController;
    controller = Get.find<ReviewController>();

    /// 确保组件创建完后再播放音乐
    WidgetsBinding.instance.addPostFrameCallback((_) {
      OxAudioController.to.play(
          controller.getAssetUrl(CardAssetType.secondaryAudio),
          isForceListening: true);
    });
  }

  bool _checkResult() {
    return (((maxSimilarityScore ?? 0) > 0.6 || tryTimes >= maxTryTimes) ||
        effectiveTry >= 3);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return GetBuilder<OxAudioController>(
              init: OxAudioController(),
              builder: (_) {
                return Column(
                  children: [
                    Expanded(
                        child: Container(
                      margin: EdgeInsets.all(AppTheme.margin),
                      child: Column(
                        children: [
                          Expanded(child: _buidCardInfo()),
                          SizedBox(height: 8.w),
                          _buildWaveOrRateBar(),
                          SizedBox(height: 8.w),
                        ],
                      ),
                    )),
                    OxAudioController.to.isForceListening
                        ? const Sbutton(
                            onPressed: null,
                            size: SbuttonSize.large,
                            shape: SbuttonShape.outline,
                            width: double.infinity,
                            child: OxText("请仔细聆听..."),
                          )
                        : Row(
                            children: [
                              Expanded(child: _buildSpeechActionBar()),
                              if (_checkResult())
                                Expanded(child: _buildResultBar())
                            ],
                          ),
                  ],
                );
              });
        });
  }

  Widget _buildPlayButton({required Icon icon, double speed = 1.0}) {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Sbutton.icon(
              icon: icon,
              onPressed: () {
                if (isRecording || OxAudioController.to.isPlaying) return;
                OxAudioController.to.play(
                    ChvCardAsset.getAssetUrl(controller.curCard.cardAssets,
                        CardAssetType.primaryAudio),
                    isForceListening: true,
                    speed: speed);
              });
        });
  }

  _buildSpeechActionBar() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          if (isScoring) {
            return const Sbutton(
                size: SbuttonSize.large,
                shape: SbuttonShape.outline,
                backgroundColor: AppTheme.primary,
                onPressed: null,
                child: OxText("录音中..."));
          } else if (isRecording) {
            return Sbutton(
                size: SbuttonSize.large,
                shape: SbuttonShape.outline,
                backgroundColor: AppTheme.primary,
                child: const OxText("停止录音"),
                onPressed: () async {
                  if (timer.isActive) timer.cancel();
                  await _stopRecording();
                });
          } else {
            return Sbutton(
              size: SbuttonSize.large,
              shape: SbuttonShape.outline,
              backgroundColor: AppTheme.primary,
              onPressed: _startRecording,
              child: const OxText("点击录音"),
            );
          }
        });
  }

  Widget _buildResultBar() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Sbutton(
            onPressed: (_checkResult())
                ? () {
                    controller.toPageIndex();
                    // controller.onResultButtonClick(true);
                    _resetData();
                  }
                : null,
            size: SbuttonSize.large,
            shape: SbuttonShape.outline,
            backgroundColor: Colors.green,
            child: const OxText("提交"),
          );
        });
  }

  Future _startRecording() async {
    setState(() {
      isAllowClick = false;
      isRecording = true;
    });
    recordTime = DateTime.now();
    await _stopPlayOrRecord();
    await Ssound().startRecord();
    timer = Timer(_getTimeOut(), () async {
      await _stopRecording();
    });
  }

  Future _stopRecording() async {
    await Ssound.to.stopRecord();
    if (DateTime.now().difference(recordTime).inMilliseconds < 800) {
      ShowToast.fail('录音时间太短');
      setState(() {
        isRecording = false;
      });
      return;
    }

    /// 更新界面状态
    setState(() {
      isRecording = Ssound.to.isRecording;
      isScoring = true;
    });

    /// 发送录音
    try {
      OxAudioController.to.play(
        controller.getAssetUrl(CardAssetType.primaryAudio),
        isForceListening: true,
      );
      // ChvSound.to.startPlay();
      double result = await controller.getWhisperResult() ?? "";
      setState(() {
        isScoring = false;
        similarityScore = result;

        /// update maxSimilarityScore
        maxSimilarityScore = max(maxSimilarityScore ?? 0, similarityScore ?? 0);
        Console.log("similarityScore:$similarityScore");
        tryTimes++;
        if ((similarityScore ?? 0) > 0.2) {
          effectiveTry++;
        }

        /// 重试次数+1
      });
    } catch (e) {
      Console.log(e);
      setState(() {
        isScoring = false;
      });
    }
  }

  _getTimeOut() {
    Duration duration = OxAudioController.to.duration;
    if (duration == Duration.zero) {
      Console.log(duration);
      return const Duration(seconds: 5);
    } else {
      return duration + const Duration(seconds: 2);
    }
  }

  /// reset data
  _resetData() {
    setState(() {
      maxSimilarityScore = null;
      similarityScore = null;
      resultString = "";
      tryTimes = 0;
    });
  }

  _buildWaveOrRateBar() {
    return SizedBox(
      height: 60,
      child: isScoring
          ? SpinKitCircle(
              color: AppTheme.primary,
              size: IconSize.titleXL,
            )
          : isRecording
              ? ChvAudioWave()
              : similarityScore != null
                  ? _buildRateBar()
                  : Container(),
    );
  }

  _buidCardInfo() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Column(
            children: [
              if (controller.getAssetUrl(CardAssetType.primaryImage) != null)
                Expanded(
                    child: Simage(
                  url: controller.getAssetUrl(CardAssetType.primaryImage) ?? "",
                  fit: BoxFit.contain,
                )),
              SizedBox(height: 8.w),
              _buildAuidoText(),
            ],
          );
        });
  }

  Widget _buildAuidoText() {
    return GetBuilder<ReviewController>(
        init: ReviewController(),
        builder: (controller) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildPlayButton(
                  icon: Icon(
                Icons.volume_up,
                size: IconSize.titleL,
                color: Theme.of(Get.context!).colorScheme.primary,
              )),
              Expanded(
                  child: Column(
                children: [
                  EasyRichText("${controller.curCard.question}",
                      defaultStyle: TextStyle(fontSize: FontSize.title),
                      textAlign: TextAlign.center,
                      patternList: [
                        ...controller.unmatch.map((e) {
                          return EasyRichTextPattern(
                            targetString: e,
                            style: TextStyle(
                                color: Theme.of(Get.context!).colorScheme.error,
                                fontWeight: FontWeight.bold),
                          );
                        }).toList(),
                      ]),
                  OxText(
                    controller.curCard.answer ?? "",
                    fontSize: FontSize.body,
                  )
                ],
              )),
              _buildPlayButton(
                  icon: Icon(
                    Icons.hourglass_top_outlined,
                    size: IconSize.titleL,
                    color: AppTheme.primary,
                  ),
                  speed: 0.6),
            ],
          );
        });
  }

  Widget _buildRateBar() {
    return Column(
      children: [
        // ChvText(
        //     "${similarityScore?.toStringAsFixed(2)}/${maxSimilarityScore?.toStringAsFixed(2)}"),
        ChvRate(
          initialValue: (similarityScore ?? 0) * 5,
          readOnly: true,
          allowHalf: true,
        ),
        SizedBox(height: 16.w),
      ],
    );
  }
}
