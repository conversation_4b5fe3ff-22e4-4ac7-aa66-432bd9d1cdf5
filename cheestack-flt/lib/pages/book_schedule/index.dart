library book_schedule;

import 'package:cheestack_flt/apis/index.dart';
import 'package:cheestack_flt/common/data/index.dart';
import 'package:cheestack_flt/controllers/load_state_controller.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

part 'controller.dart';
part 'view.dart';
