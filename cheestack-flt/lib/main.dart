import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/global.dart';
import 'package:cheestack_flt/i18n/index.dart';
import 'package:cheestack_flt/routes/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lifecycle/lifecycle.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'common/data/index.dart';

void main() {
  Global().init().then((_) => runApp(const MainApp()));
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(
        DesignSize.designWidth,
        DesignSize.deSignHeight,
      ), // common size
      builder: (context, child) => RefreshConfiguration(
        headerBuilder: () => const ClassicHeader(
          // 刷新的图标
          refreshingIcon: CupertinoActivityIndicator(),
        ),
        footerBuilder: () => const ClassicFooter(
          // 读取中的图标
          loadingIcon: CupertinoActivityIndicator(),
        ),
        hideFooterWhenNotFull: true,
        child: GestureDetector(
          onTap: () {
            if (MediaQuery.of(Get.context!).viewInsets.bottom != 0) {
              debugPrint('*** bottom == 0; ***');
              FocusScope.of(Get.context!).requestFocus(FocusNode());
            }
          },
          child: GetMaterialApp(
            // hide top right `debug` lable
            debugShowCheckedModeBanner: false,
            title: '芝士堆',
            theme: AppTheme.light(),
            darkTheme: AppTheme.dark(),
            // follow the system thememode (light or dark)
            themeMode: AppTheme.mode,
            popGesture: true,
            transitionDuration: const Duration(milliseconds: 450),
            defaultTransition: Transition.cupertino,
            // initialRoute: AppRoutes.auth,
            initialRoute: AppRoutes.splash,
            getPages: AppRoutes.pages,
            navigatorObservers: [AppRoutes.observer, defaultLifecycleObserver],
            builder: EasyLoading.init(builder: (context, child) {
              EasyLoading.instance
                ..displayDuration = const Duration(milliseconds: 1000)
                ..indicatorType = EasyLoadingIndicatorType.ring
                ..loadingStyle = EasyLoadingStyle.custom
                ..radius = 20.w
                ..boxShadow = [
                  BoxShadow(
                    color:
                        Theme.of(context).colorScheme.shadow.withValues(red: 0, green: 0, blue: 0, alpha: 0.15),
                    offset: const Offset(0, 0),
                    blurRadius: 20.w,
                  ),
                ]
                ..progressColor = Colors.transparent
                ..contentPadding = EdgeInsets.all(20.w)
                ..backgroundColor =
                    Theme.of(context).brightness == Brightness.light
                        ? Theme.of(context).colorScheme.surface
                        : Theme.of(context).colorScheme.tertiary
                ..indicatorColor = Colors.transparent
                ..textColor = Theme.of(context).colorScheme.onSurface
                ..textStyle = TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 17.w,
                  color: Theme.of(context).colorScheme.onSurface,
                )
                ..maskType = EasyLoadingMaskType.clear
                ..maskColor = const Color(0xFF09101D).withValues(red: 0, green: 0, blue: 0, alpha: 0.7)
                ..userInteractions = true
                ..successWidget = const CustomToastSuccess()
                ..errorWidget = const CustomToastFail()
                ..indicatorWidget = CustomLoadingIndicator(size: 60.w)
                ..dismissOnTap = false;
              return ScrollConfiguration(
                behavior: NoShadowScrollBehavior(),
                child: child ?? const Material(),
              );
            }),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: Translation.supported,
            fallbackLocale: Translation.fallback,
            locale: ConfigStore.to.locale,
            translations: Translation(),
          ),
        ),
      ),
    );
  }
}

class NoShadowScrollBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    switch (getPlatform(context)) {
      case TargetPlatform.iOS:
      case TargetPlatform.macOS:
        return child;
      case TargetPlatform.android:
        return GlowingOverscrollIndicator(
          showLeading: false,
          showTrailing: false,
          axisDirection: details.direction,
          color: Theme.of(context).colorScheme.primary,
          child: child,
        );
      case TargetPlatform.fuchsia:
      case TargetPlatform.linux:
      case TargetPlatform.windows:
        return GlowingOverscrollIndicator(
          showLeading: false,
          showTrailing: false,
          axisDirection: details.direction,
          color: Theme.of(context).colorScheme.primary,
          child: child,
        );
    }
  }
}
