import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/features/auth/apis/auth_api.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/shared/utils/http_client/index.dart';

void main() {
  group('Global 依赖注入测试', () {
    setUpAll(() {
      Get.testMode = true;
    });

    setUp(() {
      // 重置GetX状态
      Get.reset();

      // 模拟Global.init()中的依赖注入顺序
      // 1. 初始化HTTP客户端
      HttpClient.instance.init(const HttpClientConfig(
        baseUrl: 'https://api.test.com',
        enableCache: false,
        enableRetry: false,
      ));

      // 2. 先注册API服务
      Get.lazyPut<AuthApiService>(() => AuthApiService());

      // 3. 然后注册控制器
      Get.put<AuthController>(AuthController());
    });

    tearDown(() {
      Get.reset();
    });

    test('AuthApiService 应该在 AuthController 之前注册', () {
      // 验证AuthApiService已经注册
      expect(Get.isRegistered<AuthApiService>(), isTrue);

      // 验证AuthController已经注册
      expect(Get.isRegistered<AuthController>(), isTrue);

      // 验证AuthController能够成功获取AuthApiService
      final authController = Get.find<AuthController>();
      expect(authController, isNotNull);

      // 验证AuthApiService可以被获取
      final authApiService = Get.find<AuthApiService>();
      expect(authApiService, isNotNull);
    });

    test('AuthController 初始化应该成功', () {
      final authController = Get.find<AuthController>();

      // 验证控制器基本属性
      expect(authController.isAgreed, isFalse);
      expect(authController.isSendingCode, isFalse);
      expect(authController.countdown, equals(0));
      expect(authController.canSendCode, isTrue);
    });

    test('依赖注入顺序应该正确', () {
      // 这个测试验证了我们修复的问题：
      // AuthApiService 必须在 AuthController 之前注册

      // 1. 验证API服务已注册
      expect(() => Get.find<AuthApiService>(), returnsNormally);

      // 2. 验证控制器已注册且能正常工作
      expect(() => Get.find<AuthController>(), returnsNormally);

      // 3. 验证控制器能访问API服务
      final controller = Get.find<AuthController>();
      expect(controller, isA<AuthController>());
    });

    test('HTTP客户端应该正确初始化', () {
      // 验证HTTP客户端实例存在
      expect(HttpClient.instance, isNotNull);

      // 验证HTTP客户端可以正常工作
      expect(() => HttpClient.instance, returnsNormally);
    });
  });
}
