import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/i18n/index.dart';

void main() {
  group('国际化修复验证测试', () {
    test('中文翻译应该包含所有必要的key', () {
      // 验证听力相关的翻译
      expect(zhCN[LocaleKeys.listeningTitle], equals('磨耳朵'));
      expect(zhCN[LocaleKeys.listeningNoAudio], equals('无音频'));
      expect(zhCN[LocaleKeys.navListening], equals('磨耳朵'));
      
      // 验证反馈相关的翻译
      expect(zhCN[LocaleKeys.settingsHelpFeedback], equals('帮助与反馈'));
      
      // 验证这些key确实存在
      expect(zhCN.containsKey(LocaleKeys.listeningTitle), isTrue);
      expect(zhCN.containsKey(LocaleKeys.listeningNoAudio), isTrue);
      expect(zhCN.containsKey(LocaleKeys.navListening), isTrue);
      expect(zhCN.containsKey(LocaleKeys.settingsHelpFeedback), isTrue);
    });

    test('英文翻译应该包含所有必要的key', () {
      // 验证听力相关的翻译
      expect(enUS[LocaleKeys.listeningTitle], isNotNull);
      expect(enUS[LocaleKeys.listeningNoAudio], isNotNull);
      expect(enUS[LocaleKeys.navListening], isNotNull);
      
      // 验证反馈相关的翻译
      expect(enUS[LocaleKeys.settingsHelpFeedback], isNotNull);
      
      // 验证这些key确实存在
      expect(enUS.containsKey(LocaleKeys.listeningTitle), isTrue);
      expect(enUS.containsKey(LocaleKeys.listeningNoAudio), isTrue);
      expect(enUS.containsKey(LocaleKeys.navListening), isTrue);
      expect(enUS.containsKey(LocaleKeys.settingsHelpFeedback), isTrue);
    });

    test('LocaleKeys应该定义所有必要的常量', () {
      // 验证LocaleKeys类中定义了所有必要的常量
      expect(LocaleKeys.listeningTitle, equals('listening_title'));
      expect(LocaleKeys.listeningNoAudio, equals('listening_no_audio'));
      expect(LocaleKeys.navListening, equals('nav_listening'));
      expect(LocaleKeys.settingsHelpFeedback, equals('settings_help_feedback'));
    });

    test('Translation类应该正确配置', () {
      final translation = Translation();
      final keys = translation.keys;
      
      // 验证支持的语言
      expect(keys.containsKey('zh'), isTrue);
      expect(keys.containsKey('en'), isTrue);
      
      // 验证中文翻译
      expect(keys['zh'], equals(zhCN));
      expect(keys['en'], equals(enUS));
      
      // 验证支持的Locale
      expect(Translation.supported.length, equals(2));
      expect(Translation.supported.any((locale) => locale.languageCode == 'zh'), isTrue);
      expect(Translation.supported.any((locale) => locale.languageCode == 'en'), isTrue);
      
      // 验证fallback语言
      expect(Translation.fallback.languageCode, equals('en'));
    });
  });
}
