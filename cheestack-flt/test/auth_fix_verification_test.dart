import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/features/auth/apis/auth_api.dart';
import 'package:cheestack_flt/shared/utils/http_client/index.dart';

void main() {
  group('Auth修复验证测试', () {
    setUpAll(() {
      Get.testMode = true;
    });

    setUp(() {
      // 重置GetX状态
      Get.reset();

      // 初始化HTTP客户端
      HttpClient.instance.init(const HttpClientConfig(
        baseUrl: 'https://api.test.com',
        enableCache: false,
        enableRetry: false,
      ));

      // 注册API服务
      Get.lazyPut<AuthApiService>(() => AuthApiService());
    });

    tearDown(() {
      Get.reset();
    });

    test('AuthApiService 应该能正确注册和获取', () {
      // 验证AuthApiService可以被注册
      expect(Get.isRegistered<AuthApiService>(), isTrue);

      // 验证AuthApiService可以被获取
      final authApiService = Get.find<AuthApiService>();
      expect(authApiService, isNotNull);
      expect(authApiService, isA<AuthApiService>());
    });

    test('getCaptcha 方法应该使用正确的参数', () {
      final authApiService = Get.find<AuthApiService>();

      // 验证方法存在
      expect(authApiService.getCaptcha, isA<Function>());

      // 验证参数格式
      const phone = '13046364488';
      expect(phone, isA<String>());
      expect(phone.length, equals(11));
      expect(phone.startsWith('1'), isTrue);
    });

    test('quickLogin 方法应该使用正确的参数', () {
      final authApiService = Get.find<AuthApiService>();

      // 验证方法存在
      expect(authApiService.quickLogin, isA<Function>());

      // 验证参数格式
      const phone = '13046364488';
      const code = '8361';
      expect(phone, isA<String>());
      expect(code, isA<String>());
      expect(phone.length, equals(11));
      expect(code.length, equals(4));
    });

    test('API服务应该正确处理字段映射', () {
      // 验证我们修复的字段映射问题
      // getCaptcha: phone -> mobile
      // quickLogin: phone -> mobile, code -> captcha

      const testData = {
        'phone': '13046364488',
        'code': '8361',
      };

      // 转换为服务器期望的格式
      final captchaData = {'mobile': testData['phone']};
      final loginData = {
        'mobile': testData['phone'],
        'captcha': testData['code'],
      };

      expect(captchaData['mobile'], equals('13046364488'));
      expect(captchaData.containsKey('mobile'), isTrue);
      expect(captchaData.containsKey('phone'), isFalse);

      expect(loginData['mobile'], equals('13046364488'));
      expect(loginData['captcha'], equals('8361'));
      expect(loginData.containsKey('mobile'), isTrue);
      expect(loginData.containsKey('captcha'), isTrue);
      expect(loginData.containsKey('phone'), isFalse);
      expect(loginData.containsKey('code'), isFalse);
    });

    test('依赖注入顺序修复验证', () {
      // 这个测试验证了我们在global.dart中的修复：
      // API服务必须在控制器之前注册

      // 1. 重置状态
      Get.reset();

      // 2. 初始化HTTP客户端
      HttpClient.instance.init(const HttpClientConfig(
        baseUrl: 'https://api.test.com',
        enableCache: false,
        enableRetry: false,
      ));

      // 3. 先注册API服务（模拟global.dart中的修复）
      Get.lazyPut<AuthApiService>(() => AuthApiService());

      // 4. 验证API服务已注册
      expect(Get.isRegistered<AuthApiService>(), isTrue);

      // 5. 验证可以正常获取API服务
      expect(() => Get.find<AuthApiService>(), returnsNormally);

      final apiService = Get.find<AuthApiService>();
      expect(apiService, isNotNull);
      expect(apiService.serviceName, equals('AuthApiService'));
      expect(apiService.basePath, equals('/v1/auth'));
    });
  });
}
