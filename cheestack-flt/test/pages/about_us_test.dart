import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/i18n/index.dart';

void main() {
  group('关于我们页面测试', () {

    test('国际化键值应该正确定义', () {
      // 验证关于我们相关的国际化键值
      expect(LocaleKeys.aboutUsTitle, equals('about_us_title'));
      expect(LocaleKeys.aboutUsAppName, equals('about_us_app_name'));
      expect(LocaleKeys.aboutUsContact, equals('about_us_contact'));
      expect(LocaleKeys.aboutUsContactWechat, equals('about_us_contact_wechat'));
      expect(LocaleKeys.aboutUsContactQQ, equals('about_us_contact_qq'));
      expect(LocaleKeys.aboutUsContactEmail, equals('about_us_contact_email'));
    });

    test('中文翻译应该包含关于我们的所有文本', () {
      // 验证中文翻译
      expect(zhCN[LocaleKeys.aboutUsTitle], equals('关于芝士堆'));
      expect(zhCN[LocaleKeys.aboutUsAppName], equals('芝士堆'));
      expect(zhCN[LocaleKeys.aboutUsContact], equals('联系我们'));
      expect(zhCN[LocaleKeys.aboutUsContactWechat], equals('微信'));
      expect(zhCN[LocaleKeys.aboutUsContactQQ], equals('QQ'));
      expect(zhCN[LocaleKeys.aboutUsContactEmail], equals('邮箱'));
    });

    test('英文翻译应该包含关于我们的所有文本', () {
      // 验证英文翻译
      expect(enUS[LocaleKeys.aboutUsTitle], equals('About CheeStack'));
      expect(enUS[LocaleKeys.aboutUsAppName], equals('CheeStack'));
      expect(enUS[LocaleKeys.aboutUsContact], equals('Contact Us'));
      expect(enUS[LocaleKeys.aboutUsContactWechat], equals('WeChat'));
      expect(enUS[LocaleKeys.aboutUsContactQQ], equals('QQ'));
      expect(enUS[LocaleKeys.aboutUsContactEmail], equals('Email'));
    });
  });
}
