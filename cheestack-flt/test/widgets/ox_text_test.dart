import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/widgets/index.dart';

void main() {
  group('OxText Widget Tests', () {
    testWidgets('应该显示基本文本', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText('Hello World'),
          ),
        ),
      );

      expect(find.text('Hello World'), findsOneWidget);
    });

    testWidgets('应该使用默认的bodyMedium样式', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText('Default Style'),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      
      // 验证文本内容正确
      expect(textWidget.data, 'Default Style');
      // 验证样式不为空
      expect(textWidget.style, isNotNull);
    });

    testWidgets('应该正确应用titleLarge样式', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText(
              'Title Text',
              style: OxTextStyle.titleLarge,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      
      // 验证文本内容正确
      expect(textWidget.data, 'Title Text');
      // 验证样式不为空
      expect(textWidget.style, isNotNull);
    });

    testWidgets('应该正确应用自定义颜色', (WidgetTester tester) async {
      const customColor = Colors.red;
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText(
              'Colored Text',
              color: customColor,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.style?.color, customColor);
    });

    testWidgets('应该正确应用自定义字体大小', (WidgetTester tester) async {
      const customFontSize = 24.0;
      
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText(
              'Custom Size Text',
              fontSize: customFontSize,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.style?.fontSize, customFontSize);
    });

    testWidgets('应该正确处理maxLines和overflow', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 100,
              child: OxText(
                'This is a very long text that should overflow',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.maxLines, 2);
      expect(textWidget.overflow, TextOverflow.ellipsis);
    });

    testWidgets('应该正确处理空文本', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText(null),
          ),
        ),
      );

      expect(find.text(''), findsOneWidget);
    });

    testWidgets('应该正确应用文本对齐', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText(
              'Centered Text',
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.textAlign, TextAlign.center);
    });

    testWidgets('应该正确应用文本装饰', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: OxText(
              'Underlined Text',
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      );

      final textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.style?.decoration, TextDecoration.underline);
    });
  });
}
