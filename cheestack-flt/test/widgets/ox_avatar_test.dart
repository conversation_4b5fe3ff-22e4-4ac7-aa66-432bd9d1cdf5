import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cheestack_flt/widgets/index.dart';

void main() {
  group('OxAvatar 组件测试', () {
    testWidgets('应该显示默认头像当没有URL时', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: null,
                diameter: 120,
              ),
            ),
          ),
        ),
      );

      // 应该找到默认的person图标
      expect(find.byIcon(Icons.person), findsOneWidget);
    });

    testWidgets('应该正确设置尺寸', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar.large(
                url: null,
              ),
            ),
          ),
        ),
      );

      // 验证组件存在
      expect(find.byType(OxAvatar), findsOneWidget);
    });

    testWidgets('应该响应点击事件', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: null,
                diameter: 120,
                onTap: () {
                  tapped = true;
                },
              ),
            ),
          ),
        ),
      );

      // 点击头像
      await tester.tap(find.byType(OxAvatar));
      await tester.pump();

      expect(tapped, true);
    });

    testWidgets('应该显示边框当showBorder为true时', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: null,
                diameter: 120,
                showBorder: true,
              ),
            ),
          ),
        ),
      );

      // 验证组件存在
      expect(find.byType(OxAvatar), findsOneWidget);
      expect(find.byType(Container), findsWidgets);
    });

    test('应该正确识别本地文件路径', () {
      const avatar = OxAvatar(url: null, diameter: 120);
      
      // 测试本地文件路径识别
      expect(avatar._isLocalFile('/var/mobile/test.jpg'), true);
      expect(avatar._isLocalFile('file:///var/mobile/test.jpg'), true);
      expect(avatar._isLocalFile('https://example.com/image.jpg'), false);
      expect(avatar._isLocalFile('http://example.com/image.jpg'), false);
    });

    test('应该正确计算尺寸', () {
      // 测试预设尺寸
      const largeAvatar = OxAvatar.large(url: null);
      const mediumAvatar = OxAvatar.medium(url: null);
      const smallAvatar = OxAvatar.small(url: null);
      
      expect(largeAvatar.size, OxAvatarSize.large);
      expect(mediumAvatar.size, OxAvatarSize.medium);
      expect(smallAvatar.size, OxAvatarSize.small);
      
      // 测试自定义尺寸
      const customAvatar = OxAvatar(url: null, diameter: 100);
      expect(customAvatar.diameter, 100);
    });

    test('应该正确设置形状', () {
      const circleAvatar = OxAvatar(url: null, shape: OxAvatarShape.circle);
      const radiusAvatar = OxAvatar(url: null, shape: OxAvatarShape.radius);
      
      expect(circleAvatar.shape, OxAvatarShape.circle);
      expect(radiusAvatar.shape, OxAvatarShape.radius);
    });
  });

  group('EditableOxAvatar 组件测试', () {
    testWidgets('应该显示编辑按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: EditableOxAvatar(
                avatarUrl: null,
                size: 120,
                onEdit: () {},
              ),
            ),
          ),
        ),
      );

      // 应该找到相机图标
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('应该响应编辑按钮点击', (WidgetTester tester) async {
      bool editTapped = false;
      
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: EditableOxAvatar(
                avatarUrl: null,
                size: 120,
                onEdit: () {
                  editTapped = true;
                },
              ),
            ),
          ),
        ),
      );

      // 点击编辑按钮
      await tester.tap(find.byIcon(Icons.camera_alt));
      await tester.pump();

      expect(editTapped, true);
    });

    testWidgets('应该包含OxAvatar组件', (WidgetTester tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: EditableOxAvatar(
                avatarUrl: null,
                size: 120,
              ),
            ),
          ),
        ),
      );

      // 应该包含OxAvatar组件
      expect(find.byType(OxAvatar), findsOneWidget);
      expect(find.byType(EditableOxAvatar), findsOneWidget);
    });
  });
}

// 扩展OxAvatar以便测试私有方法
extension OxAvatarTest on OxAvatar {
  bool _isLocalFile(String path) {
    return path.startsWith('/') ||
        path.startsWith('file://') ||
        (!path.startsWith('http://') && !path.startsWith('https://'));
  }
}
