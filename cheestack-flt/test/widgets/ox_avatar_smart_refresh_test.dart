import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cheestack_flt/widgets/index.dart';

void main() {
  group('OxAvatar 智能刷新测试', () {
    testWidgets('相同URL不应该重新构建组件', (WidgetTester tester) async {
      const testUrl = 'https://example.com/avatar.jpg';
      int buildCount = 0;
      
      // 创建一个包装器来计算构建次数
      Widget buildTestWidget(String? url) {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  buildCount++;
                  return OxAvatar(
                    url: url,
                    diameter: 120,
                    disableCache: false, // 启用缓存以测试智能刷新
                  );
                },
              ),
            ),
          ),
        );
      }

      // 第一次构建
      await tester.pumpWidget(buildTestWidget(testUrl));
      expect(buildCount, 1);

      // 相同URL，不应该重新构建内部组件
      await tester.pumpWidget(buildTestWidget(testUrl));
      expect(buildCount, 2); // 外层Builder会重新构建，但OxAvatar内部应该使用缓存

      // 验证组件存在
      expect(find.byType(OxAvatar), findsOneWidget);
    });

    testWidgets('不同URL应该重新构建组件', (WidgetTester tester) async {
      const testUrl1 = 'https://example.com/avatar1.jpg';
      const testUrl2 = 'https://example.com/avatar2.jpg';
      
      Widget buildTestWidget(String? url) {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: url,
                diameter: 120,
                disableCache: false,
              ),
            ),
          ),
        );
      }

      // 第一次构建
      await tester.pumpWidget(buildTestWidget(testUrl1));
      expect(find.byType(OxAvatar), findsOneWidget);

      // 不同URL，应该重新构建
      await tester.pumpWidget(buildTestWidget(testUrl2));
      expect(find.byType(OxAvatar), findsOneWidget);
    });

    testWidgets('禁用缓存时应该总是重新构建', (WidgetTester tester) async {
      const testUrl = 'https://example.com/avatar.jpg';
      
      Widget buildTestWidget() {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: testUrl,
                diameter: 120,
                disableCache: true, // 禁用缓存
              ),
            ),
          ),
        );
      }

      // 第一次构建
      await tester.pumpWidget(buildTestWidget());
      expect(find.byType(OxAvatar), findsOneWidget);

      // 即使相同URL，禁用缓存时也应该重新构建
      await tester.pumpWidget(buildTestWidget());
      expect(find.byType(OxAvatar), findsOneWidget);
    });

    testWidgets('从null到有URL应该重新构建', (WidgetTester tester) async {
      const testUrl = 'https://example.com/avatar.jpg';
      
      Widget buildTestWidget(String? url) {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: url,
                diameter: 120,
              ),
            ),
          ),
        );
      }

      // 第一次构建 - null URL
      await tester.pumpWidget(buildTestWidget(null));
      expect(find.byIcon(Icons.person), findsOneWidget); // 应该显示默认头像

      // 设置URL - 应该重新构建
      await tester.pumpWidget(buildTestWidget(testUrl));
      expect(find.byType(OxAvatar), findsOneWidget);
    });

    testWidgets('从有URL到null应该重新构建', (WidgetTester tester) async {
      const testUrl = 'https://example.com/avatar.jpg';
      
      Widget buildTestWidget(String? url) {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          child: MaterialApp(
            home: Scaffold(
              body: OxAvatar(
                url: url,
                diameter: 120,
              ),
            ),
          ),
        );
      }

      // 第一次构建 - 有URL
      await tester.pumpWidget(buildTestWidget(testUrl));
      expect(find.byType(OxAvatar), findsOneWidget);

      // 设置为null - 应该重新构建并显示默认头像
      await tester.pumpWidget(buildTestWidget(null));
      expect(find.byIcon(Icons.person), findsOneWidget);
    });
  });
}
