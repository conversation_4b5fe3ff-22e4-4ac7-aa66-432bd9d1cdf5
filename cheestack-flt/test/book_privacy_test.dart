import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/controllers/book_controller.dart';

void main() {
  group('BookController Privacy Tests', () {
    late BookController controller;

    setUp(() {
      controller = BookController();
    });

    test('默认隐私设置应该是free', () {
      expect(controller.privacy, equals('free'));
    });

    test('setPrivacy应该正确更新隐私设置', () {
      // 测试设置为private
      controller.setPrivacy('private');
      expect(controller.privacy, equals('private'));

      // 测试设置为paid
      controller.setPrivacy('paid');
      expect(controller.privacy, equals('paid'));

      // 测试设置为member_free
      controller.setPrivacy('member_free');
      expect(controller.privacy, equals('member_free'));

      // 测试设置为free
      controller.setPrivacy('free');
      expect(controller.privacy, equals('free'));
    });

    test('setPrivacy应该忽略null值', () {
      final originalPrivacy = controller.privacy;
      controller.setPrivacy(null);
      expect(controller.privacy, equals(originalPrivacy));
    });

    test('支持的隐私类型应该包含所有四种选项', () {
      final supportedTypes = ['free', 'private', 'paid', 'member_free'];
      
      for (final type in supportedTypes) {
        controller.setPrivacy(type);
        expect(controller.privacy, equals(type));
      }
    });
  });
}
