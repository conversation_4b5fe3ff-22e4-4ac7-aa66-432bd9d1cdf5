import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/models/index.dart';

void main() {
  group('BookDataService Integration Tests', () {
    test('should create BookModel with correct properties', () {
      // 测试BookModel的创建和属性
      const bookId = 1;
      const bookName = '测试书籍';
      const bookBrief = '这是一个测试书籍';
      const bookPrivacy = 'private';
      final createdAt = DateTime.now().toIso8601String();
      final updatedAt = DateTime.now().toIso8601String();

      final book = BookModel(
        id: bookId,
        name: bookName,
        brief: bookBrief,
        privacy: bookPrivacy,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );

      expect(book.id, equals(bookId));
      expect(book.name, equals(bookName));
      expect(book.brief, equals(bookBrief));
      expect(book.privacy, equals(bookPrivacy));
      expect(book.createdAt, equals(createdAt));
      expect(book.updatedAt, equals(updatedAt));
    });

    test('should create BookModel with copyWith method', () {
      final originalBook = BookModel(
        id: 1,
        name: '原始书籍',
        brief: '原始描述',
        privacy: 'private',
      );

      final updatedBook = originalBook.copyWith(
        name: '更新后的书籍',
        brief: '更新后的描述',
      );

      expect(updatedBook.id, equals(originalBook.id));
      expect(updatedBook.name, equals('更新后的书籍'));
      expect(updatedBook.brief, equals('更新后的描述'));
      expect(updatedBook.privacy, equals(originalBook.privacy));
    });

    test('should serialize BookModel to JSON', () {
      final book = BookModel(
        id: 1,
        name: '测试书籍',
        brief: '测试描述',
        privacy: 'private',
        createdAt: '2025-01-01T00:00:00.000Z',
        updatedAt: '2025-01-01T00:00:00.000Z',
      );

      final json = book.toJson();

      expect(json['id'], equals(1));
      expect(json['name'], equals('测试书籍'));
      expect(json['brief'], equals('测试描述'));
      expect(json['privacy'], equals('private'));
      expect(json['created_at'], equals('2025-01-01T00:00:00.000Z'));
      expect(json['updated_at'], equals('2025-01-01T00:00:00.000Z'));
    });

    test('should deserialize BookModel from JSON', () {
      final json = {
        'id': 1,
        'name': '测试书籍',
        'brief': '测试描述',
        'privacy': 'private',
        'created_at': '2025-01-01T00:00:00.000Z',
        'updated_at': '2025-01-01T00:00:00.000Z',
        'user': null,
      };

      final book = BookModel.fromJson(json);

      expect(book.id, equals(1));
      expect(book.name, equals('测试书籍'));
      expect(book.brief, equals('测试描述'));
      expect(book.privacy, equals('private'));
      expect(book.createdAt, equals('2025-01-01T00:00:00.000Z'));
      expect(book.updatedAt, equals('2025-01-01T00:00:00.000Z'));
      expect(book.user, isNull);
    });
  });
}
