import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';

// 生成Mock类
@GenerateNiceMocks([
  MockSpec<ApiSyncService>(),
  MockSpec<BookDataService>(),
  MockSpec<CardDataService>(),
])
import 'sync_service_test.mocks.dart';

void main() {
  group('同步服务测试', () {
    late MockApiSyncService mockApiSyncService;
    late MockBookDataService mockBookDataService;
    late MockCardDataService mockCardDataService;

    setUp(() {
      // 重置GetX状态
      Get.reset();

      mockApiSyncService = MockApiSyncService();
      mockBookDataService = MockBookDataService();
      mockCardDataService = MockCardDataService();
    });

    tearDown(() {
      Get.reset();
    });

    group('书籍同步测试', () {
      test('应该正确处理空的用户ID', () async {
        // 安排
        when(mockApiSyncService.syncAllData(userId: ''))
            .thenAnswer((_) async => throw Exception('User ID is empty'));

        // 执行和验证
        expect(
          () async => await mockApiSyncService.syncAllData(userId: ''),
          throwsA(isA<Exception>()),
        );
      });

      test('应该正确处理null字段', () async {
        // 安排
        final testBook = BookModel(
          id: 1,
          name: null, // 测试null字段
          brief: null,
          cover: null,
          privacy: null,
          createdAt: null,
          updatedAt: null,
        );

        when(mockBookDataService.createBook(
          name: anyNamed('name'),
          brief: anyNamed('brief'),
          cover: anyNamed('cover'),
          privacy: anyNamed('privacy'),
        )).thenAnswer((_) async => testBook);

        // 执行
        final result = await mockBookDataService.createBook(
          name: '',
          brief: '',
          cover: '',
          privacy: 'free',
        );

        // 验证
        expect(result, isNotNull);
        expect(result?.id, equals(1));
      });

      test('应该正确处理同步失败的情况', () async {
        // 安排
        when(mockApiSyncService.syncTable('books'))
            .thenAnswer((_) async => false);

        // 执行
        final result = await mockApiSyncService.syncTable('books');

        // 验证
        expect(result, isFalse);
      });
    });

    group('卡片同步测试', () {
      test('应该正确处理卡片数据', () async {
        // 安排
        final testCard = CardModel(
          id: 1,
          type: 'general',
          typeVersion: 1,
          title: '测试卡片',
          question: '问题',
          answer: '答案',
          extra: null,
          scheduleId: null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockCardDataService.createCard(
          bookId: anyNamed('bookId'),
          type: anyNamed('type'),
          title: anyNamed('title'),
          question: anyNamed('question'),
          answer: anyNamed('answer'),
        )).thenAnswer((_) async => testCard);

        // 执行
        final result = await mockCardDataService.createCard(
          bookId: 1,
          type: 'general',
          title: '测试卡片',
          question: '问题',
          answer: '答案',
        );

        // 验证
        expect(result, isNotNull);
        expect(result?.title, equals('测试卡片'));
      });

      test('应该正确处理卡片同步', () async {
        // 安排
        when(mockApiSyncService.syncTable('cards'))
            .thenAnswer((_) async => true);

        // 执行
        final result = await mockApiSyncService.syncTable('cards');

        // 验证
        expect(result, isTrue);
      });
    });

    group('错误处理测试', () {
      test('应该正确处理网络错误', () async {
        // 安排
        when(mockApiSyncService.syncAllData())
            .thenAnswer((_) async => throw Exception('Network error'));

        // 执行和验证
        expect(
          () async => await mockApiSyncService.syncAllData(),
          throwsA(isA<Exception>()),
        );
      });

      test('应该正确处理数据库错误', () async {
        // 安排
        when(mockBookDataService.createBook(
          name: anyNamed('name'),
          brief: anyNamed('brief'),
          cover: anyNamed('cover'),
          privacy: anyNamed('privacy'),
        )).thenThrow(Exception('Database error'));

        // 执行和验证
        expect(
          () async => await mockBookDataService.createBook(
            name: '测试书籍',
            brief: '简介',
            cover: '',
            privacy: 'free',
          ),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
