import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:cheestack_flt/shared/utils/http_client/index.dart';

void main() {
  group('HttpClient 测试', () {
    late HttpClient httpClient;

    setUp(() {
      HttpClient.resetInstance();
      httpClient = HttpClient.instance;
      httpClient.init(const HttpClientConfig(
        baseUrl: 'https://api.test.com',
        connectTimeout: Duration(seconds: 5),
        receiveTimeout: Duration(seconds: 10),
        enableCache: false,
        enableRetry: false,
      ));
    });

    group('配置测试', () {
      test('应该正确初始化HTTP客户端配置', () {
        expect(httpClient.dio.options.baseUrl, equals('https://api.test.com'));
        expect(httpClient.dio.options.connectTimeout,
            equals(const Duration(seconds: 5)));
        expect(httpClient.dio.options.receiveTimeout,
            equals(const Duration(seconds: 10)));
      });

      test('应该添加必要的拦截器', () {
        final interceptors = httpClient.dio.interceptors;
        expect(interceptors.length, greaterThan(0));

        // 检查是否包含通用拦截器
        final hasCommonInterceptor =
            interceptors.any((interceptor) => interceptor is CommonInterceptor);
        expect(hasCommonInterceptor, isTrue);
      });
    });

    group('GET请求测试', () {
      test('应该返回成功响应', () async {
        // 模拟成功响应
        final mockResponse = Response(
          requestOptions: RequestOptions(path: '/test'),
          statusCode: 200,
          data: {
            'code': 1,
            'data': {'message': 'success'}
          },
        );

        // 这里需要mock dio的行为，实际测试中会使用mock库
        // 由于这是示例，我们只测试响应处理逻辑
        expect(mockResponse.statusCode, equals(200));
        expect(mockResponse.data!['code'], equals(1));
      });

      test('应该处理网络错误', () async {
        // 测试网络错误处理
        final dioError = DioException(
          requestOptions: RequestOptions(path: '/test'),
          type: DioExceptionType.connectionTimeout,
          message: '连接超时',
        );

        expect(dioError.type, equals(DioExceptionType.connectionTimeout));
        expect(dioError.message, contains('连接超时'));
      });
    });

    group('POST请求测试', () {
      test('应该正确发送POST数据', () async {
        final testData = {'name': 'test', 'value': 123};

        // 验证数据格式
        expect(testData['name'], equals('test'));
        expect(testData['value'], equals(123));
      });
    });

    group('错误处理测试', () {
      test('应该正确处理HTTP错误状态码', () {
        final error400 = HttpExceptionFactory.fromStatusCode(400, '请求参数错误');
        expect(error400, isA<BadRequestException>());
        expect(error400.message, equals('请求参数错误'));

        final error401 = HttpExceptionFactory.fromStatusCode(401, '未授权');
        expect(error401, isA<UnauthorizedException>());
        expect(error401.message, equals('未授权'));

        final error404 = HttpExceptionFactory.fromStatusCode(404, '资源未找到');
        expect(error404, isA<NotFoundException>());
        expect(error404.message, equals('资源未找到'));

        final error500 = HttpExceptionFactory.fromStatusCode(500, '服务器错误');
        expect(error500, isA<InternalServerException>());
        expect(error500.message, equals('服务器错误'));
      });

      test('应该正确处理异常类型', () {
        final networkException = const NetworkException('网络连接失败');
        expect(networkException.message, equals('网络连接失败'));
        expect(networkException.code, equals(-1));

        final timeoutException = const TimeoutException('请求超时');
        expect(timeoutException.message, equals('请求超时'));
        expect(timeoutException.code, equals(-2));

        final cancelException = const CancelException('请求取消');
        expect(cancelException.message, equals('请求取消'));
        expect(cancelException.code, equals(-3));
      });
    });
  });

  group('ApiResponse 测试', () {
    test('应该创建成功响应', () {
      final response = ApiResponse.success('test data', statusCode: 200);

      expect(response.isSuccess, isTrue);
      expect(response.isFailure, isFalse);
      expect(response.data, equals('test data'));
      expect(response.statusCode, equals(200));
      expect(response.message, isNull);
      expect(response.errorCode, isNull);
    });

    test('应该创建失败响应', () {
      final response = ApiResponse.failure(
        message: '请求失败',
        errorCode: 400,
        statusCode: 400,
      );

      expect(response.isSuccess, isFalse);
      expect(response.isFailure, isTrue);
      expect(response.data, isNull);
      expect(response.message, equals('请求失败'));
      expect(response.errorCode, equals(400));
      expect(response.statusCode, equals(400));
    });

    test('应该创建网络错误响应', () {
      final response = ApiResponse<String>.networkError('网络连接失败');

      expect(response.isSuccess, isFalse);
      expect(response.message, equals('网络连接失败'));
      expect(response.errorCode, equals(-1));
    });

    test('应该正确转换数据类型', () {
      final response = ApiResponse.success({'count': 5});
      final mappedResponse = response.map<int>((data) => data['count'] as int);

      expect(mappedResponse.isSuccess, isTrue);
      expect(mappedResponse.data, equals(5));
    });

    test('转换失败时应该返回错误响应', () {
      final response = ApiResponse.success('invalid data');
      final mappedResponse =
          response.map<int>((data) => throw Exception('转换失败'));

      expect(mappedResponse.isSuccess, isFalse);
      expect(mappedResponse.message, contains('数据转换失败'));
      expect(mappedResponse.errorCode, equals(-100));
    });

    test('失败响应转换时应该保持失败状态', () {
      final response = ApiResponse<String>.failure(message: '原始错误');
      final mappedResponse = response.map<int>((data) => 123);

      expect(mappedResponse.isSuccess, isFalse);
      expect(mappedResponse.message, equals('原始错误'));
    });

    test('requireData 在成功时应该返回数据', () {
      final response = ApiResponse.success('test data');
      expect(response.requireData, equals('test data'));
    });

    test('requireData 在失败时应该抛出异常', () {
      final response = ApiResponse<String>.failure(message: '请求失败');
      expect(() => response.requireData, throwsException);
    });

    test('应该正确实现相等性比较', () {
      final response1 = ApiResponse.success('data', statusCode: 200);
      final response2 = ApiResponse.success('data', statusCode: 200);
      final response3 = ApiResponse.success('other', statusCode: 200);

      expect(response1, equals(response2));
      expect(response1, isNot(equals(response3)));
    });

    test('应该正确实现toString', () {
      final successResponse = ApiResponse.success('data', statusCode: 200);
      final failureResponse = ApiResponse<String>.failure(
        message: '错误',
        errorCode: 400,
        statusCode: 400,
      );

      expect(successResponse.toString(), contains('success'));
      expect(successResponse.toString(), contains('data'));
      expect(successResponse.toString(), contains('200'));

      expect(failureResponse.toString(), contains('failure'));
      expect(failureResponse.toString(), contains('错误'));
      expect(failureResponse.toString(), contains('400'));
    });
  });

  group('HttpClientConfig 测试', () {
    test('应该使用默认配置值', () {
      const config = HttpClientConfig();

      expect(config.baseUrl, isNull);
      expect(config.connectTimeout, equals(const Duration(seconds: 10)));
      expect(config.sendTimeout, equals(const Duration(seconds: 10)));
      expect(config.receiveTimeout, equals(const Duration(seconds: 30)));
      expect(config.enableCache, isTrue);
      expect(config.enableRetry, isTrue);
      expect(config.maxRetries, equals(3));
    });

    test('应该允许自定义配置值', () {
      const config = HttpClientConfig(
        baseUrl: 'https://custom.api.com',
        connectTimeout: Duration(seconds: 15),
        enableCache: false,
        maxRetries: 5,
      );

      expect(config.baseUrl, equals('https://custom.api.com'));
      expect(config.connectTimeout, equals(const Duration(seconds: 15)));
      expect(config.enableCache, isFalse);
      expect(config.maxRetries, equals(5));
    });
  });
}
