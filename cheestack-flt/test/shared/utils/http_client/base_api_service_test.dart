import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/shared/utils/http_client/index.dart';

// 测试用的模型类
class TestModel {
  final int id;
  final String name;

  TestModel({required this.id, required this.name});

  factory TestModel.fromJson(Map<String, dynamic> json) {
    return TestModel(
      id: json['id'] as int,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TestModel && other.id == id && other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

// 测试用的API服务类
class TestApiService extends BaseApiService {
  @override
  String get serviceName => 'TestApiService';

  @override
  String get basePath => '/api/test';
}

void main() {
  group('BaseApiService 测试', () {
    late TestApiService apiService;

    setUpAll(() {
      // 初始化GetX
      Get.testMode = true;
    });

    setUp(() {
      // 重置并初始化HTTP客户端
      HttpClient.resetInstance();
      HttpClient.instance.init(const HttpClientConfig(
        baseUrl: 'https://api.test.com',
        enableCache: false,
        enableRetry: false,
      ));

      apiService = TestApiService();
      apiService.onInit();
    });

    tearDown(() {
      Get.reset();
    });

    group('基础属性测试', () {
      test('应该正确设置服务名称和基础路径', () {
        expect(apiService.serviceName, equals('TestApiService'));
        expect(apiService.basePath, equals('/api/test'));
      });
    });

    group('CRUD操作测试', () {
      test('getList 应该返回正确的数据类型', () async {
        // 由于无法mock实际的HTTP请求，这里主要测试方法签名和参数传递
        expect(apiService.getList, isA<Function>());

        // 测试参数类型
        final queryParams = {'page': 1, 'limit': 10};
        expect(queryParams['page'], isA<int>());
        expect(queryParams['limit'], isA<int>());
      });

      test('getById 应该接受正确的参数类型', () {
        expect(apiService.getById, isA<Function>());

        // 测试ID参数可以是不同类型
        const intId = 123;
        const stringId = 'abc123';

        expect(intId, isA<int>());
        expect(stringId, isA<String>());
      });

      test('create 应该接受Map数据', () {
        expect(apiService.create, isA<Function>());

        final testData = {'name': 'test', 'value': 123};
        expect(testData, isA<Map<String, dynamic>>());
      });

      test('update 应该接受ID和数据', () {
        expect(apiService.update, isA<Function>());

        const id = 123;
        final data = {'name': 'updated'};

        expect(id, isA<int>());
        expect(data, isA<Map<String, dynamic>>());
      });

      test('deleteById 应该接受ID参数', () {
        expect(apiService.deleteById, isA<Function>());

        const id = 123;
        expect(id, isA<int>());
      });
    });

    group('通用请求方法测试', () {
      test('get 方法应该接受正确的参数', () {
        expect(apiService.get, isA<Function>());

        const endpoint = '/test/endpoint';
        final queryParams = {'param1': 'value1'};

        expect(endpoint, isA<String>());
        expect(queryParams, isA<Map<String, dynamic>>());
      });

      test('post 方法应该接受正确的参数', () {
        expect(apiService.post, isA<Function>());

        const endpoint = '/test/endpoint';
        final data = {'key': 'value'};

        expect(endpoint, isA<String>());
        expect(data, isA<Map<String, dynamic>>());
      });

      test('put 方法应该接受正确的参数', () {
        expect(apiService.put, isA<Function>());

        const endpoint = '/test/endpoint';
        final data = {'key': 'updated_value'};

        expect(endpoint, isA<String>());
        expect(data, isA<Map<String, dynamic>>());
      });

      test('delete 方法应该接受正确的参数', () {
        expect(apiService.delete, isA<Function>());

        const endpoint = '/test/endpoint';
        expect(endpoint, isA<String>());
      });
    });

    group('分页查询测试', () {
      test('getPage 应该接受分页参数', () {
        expect(apiService.getPage, isA<Function>());

        const page = 1;
        const pageSize = 20;
        final filters = {'status': 'active'};
        const orderBy = '-created_at';

        expect(page, isA<int>());
        expect(pageSize, isA<int>());
        expect(filters, isA<Map<String, dynamic>>());
        expect(orderBy, isA<String>());
      });

      test('分页参数应该有合理的默认值', () {
        // 测试默认分页参数
        const defaultPage = 1;
        const defaultPageSize = 20;

        expect(defaultPage, greaterThan(0));
        expect(defaultPageSize, greaterThan(0));
        expect(defaultPageSize, lessThanOrEqualTo(100)); // 合理的页面大小限制
      });
    });
  });

  group('PageResult 测试', () {
    test('应该正确创建分页结果', () {
      final items = [
        TestModel(id: 1, name: 'Item 1'),
        TestModel(id: 2, name: 'Item 2'),
      ];

      final pageResult = PageResult<TestModel>(
        items: items,
        total: 10,
        page: 1,
        pageSize: 2,
        totalPages: 5,
        hasNext: true,
        hasPrevious: false,
      );

      expect(pageResult.items, equals(items));
      expect(pageResult.total, equals(10));
      expect(pageResult.page, equals(1));
      expect(pageResult.pageSize, equals(2));
      expect(pageResult.totalPages, equals(5));
      expect(pageResult.hasNext, isTrue);
      expect(pageResult.hasPrevious, isFalse);
    });

    test('应该正确计算派生属性', () {
      final items = [
        TestModel(id: 1, name: 'Item 1'),
        TestModel(id: 2, name: 'Item 2'),
      ];

      final pageResult = PageResult<TestModel>(
        items: items,
        total: 10,
        page: 1,
        pageSize: 2,
        totalPages: 5,
        hasNext: true,
        hasPrevious: false,
      );

      expect(pageResult.isEmpty, isFalse);
      expect(pageResult.isNotEmpty, isTrue);
      expect(pageResult.count, equals(2));
    });

    test('空结果应该正确处理', () {
      const pageResult = PageResult<TestModel>(
        items: <TestModel>[],
        total: 0,
        page: 1,
        pageSize: 20,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false,
      );

      expect(pageResult.isEmpty, isTrue);
      expect(pageResult.isNotEmpty, isFalse);
      expect(pageResult.count, equals(0));
    });

    test('toString 应该包含关键信息', () {
      const pageResult = PageResult<TestModel>(
        items: <TestModel>[],
        total: 100,
        page: 2,
        pageSize: 20,
        totalPages: 5,
        hasNext: true,
        hasPrevious: true,
      );

      final str = pageResult.toString();
      expect(str, contains('total: 100'));
      expect(str, contains('page: 2'));
      expect(str, contains('pageSize: 20'));
      expect(str, contains('count: 0'));
    });

    test('应该正确处理边界情况', () {
      // 第一页
      const firstPage = PageResult<TestModel>(
        items: <TestModel>[],
        total: 100,
        page: 1,
        pageSize: 20,
        totalPages: 5,
        hasNext: true,
        hasPrevious: false,
      );

      expect(firstPage.hasPrevious, isFalse);
      expect(firstPage.hasNext, isTrue);

      // 最后一页
      const lastPage = PageResult<TestModel>(
        items: <TestModel>[],
        total: 100,
        page: 5,
        pageSize: 20,
        totalPages: 5,
        hasNext: false,
        hasPrevious: true,
      );

      expect(lastPage.hasPrevious, isTrue);
      expect(lastPage.hasNext, isFalse);

      // 单页结果
      const singlePage = PageResult<TestModel>(
        items: <TestModel>[],
        total: 10,
        page: 1,
        pageSize: 20,
        totalPages: 1,
        hasNext: false,
        hasPrevious: false,
      );

      expect(singlePage.hasPrevious, isFalse);
      expect(singlePage.hasNext, isFalse);
    });
  });
}
